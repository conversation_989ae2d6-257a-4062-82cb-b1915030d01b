import type { AxiosRequestConfig } from 'axios'
import axios, { AxiosError } from 'axios'
import { ElMessageBox } from 'element-plus'
import Cookies from 'js-cookie'
import qs from 'qs'
import { getDeviceType, isWXWork, query } from './utils'

function getPath() {
  const { href } = window.location
  const urlObject = new URL(href)
  const { pathname, search } = urlObject
  const path = pathname + search
  return path
}
const whiteUrls = []
const api = axios.create({
  baseURL: '/',
  withCredentials: true,
  timeout: 500000,
})

api.interceptors.request.use((config) => {
  if (config.method === 'get') {
    config.paramsSerializer = function (params) {
      return qs.stringify(params, { arrayFormat: 'repeat' })
    }
  }
  return config
})

api.interceptors.response.use(
  (response) => {
    if (response.status === 200) {
      return Promise.resolve(response)
    }
    else {
      return Promise.reject(response)
    }
  },
  (error) => {
    const isWX = isWXWork()
    const returnUrl = getPath()
    const origin = window.location.origin
    if (error && error?.response?.status === 401 && isWX) {
      window.location.href = `/api/wecom/login?host=${origin}${returnUrl}`
      return
    }
    if (error?.response?.status === 401) {
      const token = query('authtoken') || query('token') || ''
      if (token) {
        Cookies.set('authtoken', token, {
          sameSite: 'None',
          secure: true,
        })
      }
      else {
        if (getDeviceType() !== 'Mobile') {
          window.location.replace(`/login/callback?stateUrl=${returnUrl}`)
        }
        else {
          window.location.href = `/login?returnUrl=${origin}${returnUrl}`
        }
      }
    }
    return Promise.reject(error)
  },
)

class MisReqError extends Error {
  constructor(...params: any) {
    super(...params)
    this.name = 'DmpReqError'
  }
}
function request(params: AxiosRequestConfig) {
  const token = query('authtoken') || query('token') || Cookies.get('authtoken')
  const headers = {
    Authorization: params.headers?.Authorization || '',
  }
  if (token) {
    headers.Authorization = `bearer ${token}`
    Cookies.set('authtoken', token, {
      sameSite: 'None',
      secure: true,
    })
  }

  return api
    .request({ ...params, headers })
    .then((res: any) => {
      if (res.status === 200 && !whiteUrls.includes(res.config.url as string)) {
        return res.data
      }
      else {
        return res
      }
    })
    .catch((err) => {
      if (err instanceof AxiosError && err.status !== 200) {
        const { error } = err.response?.data || {}
        if (error) {
          ElMessageBox.confirm(error, '错误', {
            confirmButtonText: '确认',
            showCancelButton: false,
            type: 'error',
          })
        }
        throw err
      }
      else if (err instanceof MisReqError && err?.message) {
        console.error(err)
      }
      else {
        throw err
      }
    })
}
export default request
