export const CARD_TYPE: Record<string, any> = {
  '000': {
    title: '品类品牌商品推荐上架',
    shareTitle: '系统智能推荐物料推荐上架',
    tag: '潜在生意机会',
    desc: '根据客户的采购清单高生意机会的二级类目和品牌，匹配站内的有竞争力的商品，建议推品上架',
    share: '震坤行AI推品大脑根据您的常购清单为您精准匹配震坤行具有商品竞争力的商品，建议推品上架，震坤行竭诚为您提供更优质的MRO商品及服务；',
  },
  '001': {
    title: '常采清单中未推送的品类品牌商品推荐上架',
    shareTitle: '系统智能推荐物料推荐上架',
    tag: '潜在生意机会',
    desc: '根据客户的采购清单高生意机会的二级类目和品牌，匹配站内的有竞争力的商品，建议推品上架',
    share: '震坤行AI推品大脑根据您的常购清单为您精准匹配震坤行具有商品竞争力的商品，建议推品上架，震坤行竭诚为您提供更优质的MRO商品及服务；',
  },
  '001-nopool': {
    title: '常采清单中常购品类品牌优势商品推荐',
    shareTitle: '系统智能推荐优势物料推荐',
    tag: '潜在生意机会',
    desc: '根据客户采购清单中高生意机会的二级类目和品牌，匹配站内有竞争力的商品，建议推荐客户',
    share: '震坤行AI推品大脑根据您的常购清单为您精准匹配震坤行具有商品竞争力的商品，建议选购，震坤行竭诚为您提供更优质的MRO商品及服务；',
  },
  '002': {
    title: '常采清单中无品牌需求精选品牌商品推荐上架',
    shareTitle: '震坤行品类优势物料推荐上架',
    tag: '潜在生意机会',
    desc: '根据客户的采购清单中无品牌的商品，推荐该品类下有竞争优势的品牌，建议推品上架',
    share: '震坤行AI推品大脑根据您的常购清单的品类为您匹配该品类下震坤行具有优势的品牌的商品，建议推品上架，震坤行竭诚为您提供更优质的MRO商品及服务；',
  },
  '002-nopool': {
    title: '常采清单中常购品类中优势品牌商品推荐',
    shareTitle: '震坤行品类优势物料推荐',
    tag: '潜在生意机会',
    desc: '根据客户采购清单中无品牌的商品，推荐该品类下有竞争优势的品牌，建议推荐客户',
    share: '震坤行AI推品大脑根据您的常购清单的品类为您匹配该品类下震坤行具有优势的品牌的商品，建议选购，震坤行竭诚为您提供更优质的MRO商品及服务；',
  },
  '003': {
    title: '客户商品池异常SKU的替品建议推送上架',
    shareTitle: '已在架的异常商品替品推荐上架',
    tag: '异常商品替换',
    desc: '客户商品池中的停用或者待询价的商品系统智能识别可替换的SKU，建议替换商品上架',
    share: '震坤行AI推品大脑识别到您的商城中有sku_count个商品为停用或者待询价无法正常下单且已匹配出对应的替品SKU，建议推品上架，震坤行竭诚为您提供更优质的MRO商品及服务；',
  },
  '004': {
    title: 'industryTagName专属商品推荐上架',
    shareTitle: 'industryTagName专属商品推荐上架',
    tag: 'AI商品推荐',
    desc: '根据客户震坤行行业标签，推荐该行业下有竞争优势的品牌，建议推荐客户',
    share: '震坤行AI推品大脑根据您的行业信息为您匹配该行业下震坤行具有优势的品牌的商品，建议推品上架，震坤行竭诚为您提供更优质的MRO商品及服务；',
  },
  '004-nopool': {
    title: 'industryTagName专属商品推荐',
    shareTitle: 'industryTagName专属商品推荐',
    tag: 'AI商品推荐',
    desc: '根据客户震坤行行业标签，推荐该行业下有竞争优势的品牌，建议推荐客户',
    share: '震坤行AI推品大脑根据您的行业信息为您匹配该行业下震坤行具有优势的品牌的商品，建议选购，震坤行竭诚为您提供更优质的MRO商品及服务；',
  },
}

export const RECOMMEND_MODE = [
  { id: '001', value: '精准匹配' },
  { id: '002', value: '相似推荐' },
]

export const selected_tag = {
  recommend_mode: [{ id: -1, value: '全部' }],
  type: [{ id: -1, value: '全部' }],
  brand: [{ id: -1, value: '全部' }],
  tag: [{ id: -1, value: '全部' }],
  custom: [{ id: -1, value: '全部' }],
}
