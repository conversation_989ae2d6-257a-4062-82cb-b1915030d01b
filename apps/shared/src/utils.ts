export function debounce(fn: any, delay: number) {
  let timer: any = null
  return function (this: unknown, ...args: any[]) {
    if (timer)
      clearTimeout(timer)
    timer = setTimeout(() => {
      fn.apply(this, args)
    }, delay)
  }
}

export function query(prop: string) {
  const args = parseSearchParams(location.search)
  return args[prop] || ''
}

export function parseSearchParams(search: string) {
  const params: Record<string, any> = {}
  search
    .slice(1)
    .split('&')
    .forEach((param) => {
      const [key, value] = param.split('=')
      params[key] = decodeURIComponent(value)
    })
  return params
}

export const isWXWork = () => navigator.userAgent.toLowerCase().includes('wxwork')

export function formatMoney(money: number, desimel = 0) {
  if (desimel) {
    return money >= 10000 ? (money / 10000).toFixed(desimel) : money
  }
  return money >= 10000 ? Math.round((money / 10000)) : money
}

export function randomString(len: number): string {
  const chars = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678'
  const maxPos = chars.length
  let pwd = ''
  for (let i = 0; i < len; i++) {
    pwd += chars.charAt(Math.floor(Math.random() * maxPos))
  }
  return pwd
}

export function isTestEnv(): boolean {
  return location.hostname.includes('uat') || location.hostname.includes('localhost')
}

export function getDeviceType() {
  const userAgent = navigator.userAgent
  if (
    userAgent.includes('Android')
    || userAgent.includes('iPhone')
    || userAgent.includes('iPad')
    || userAgent.includes('iPod')
    || userAgent.includes('iOS')
  ) {
    return 'Mobile'
  }
  if (userAgent.includes('Windows') || userAgent.includes('Mac')) {
    return 'PC'
  }
  else {
    return 'Unknown'
  }
}

export function formatNumToPercent(cellValue: number) {
  return cellValue ? `${Number.parseFloat((Number(cellValue) * 100).toFixed(2))}%` : cellValue
}

export function formatPercentToNum(cellValue: number) {
  return cellValue ? Number.parseFloat((Number(cellValue) / 100).toFixed(4)) : cellValue
}

export function getPlatformType() {
  const userAgent = navigator.userAgent
  if (
    userAgent.includes('iPhone')
    || userAgent.includes('iPad')
    || userAgent.includes('iPod')
    || userAgent.includes('iOS')
  ) {
    return 'iOS'
  }
  if (userAgent.includes('Android')) {
    return 'Android'
  }
  if (userAgent.includes('Windows') || userAgent.includes('Mac')) {
    return 'PC'
  }
  else {
    return 'Unknown'
  }
}

// 将文本格式化为可换行的文本 去掉XSS攻击的标签
export function formatTextToHTML(text: string) {
  if (!text)
    return ''
  // eslint-disable-next-line regexp/no-super-linear-backtracking
  const regex = /<(\/*)([a-z0-9]+)([^>]*?)(\/*)>/gi
  return text.replace(regex, '').replace(/\n/g, '<br>')
}

export function extractNumber(str: string): number {
  // 使用正则表达式匹配数字部分
  const match = str.match(/\d+/)
  if (match) {
    // 将匹配到的数字部分转换为数字
    return Number.parseInt(match[0], 10)
  }
  else {
    return 0
  }
}

export function sendWechatMsg(user: string, content: string) {
  const myHeaders = new Headers()
  myHeaders.append('Cookie', document.cookie)
  myHeaders.append('Content-Type', 'application/json')
  const raw = JSON.stringify({
    content,
    user,
  })

  const requestOptions: RequestInit = {
    method: 'POST',
    headers: myHeaders,
    body: raw,
    redirect: 'follow',
  }

  fetch('/internal-api/wecom/send', requestOptions)
    .then(response => response.text())
    .then(result => console.warn(result))
    .catch(error => console.error(error))
}
// 数组去重，根据对象的某个属性去重
export function uniqueArrayByProperty(arr: any[], property: string) {
  const uniqueSet = new Set()
  return arr.filter((item) => {
    const propertyValue = item[property]
    if (!uniqueSet.has(propertyValue)) {
      uniqueSet.add(propertyValue)
      return true
    }
    return false
  })
}
