export function unoCustom(rules) {
  return Object.entries(rules)
}

/**
 * css声明组合多的可考虑此方式
 * key: 类名，value：CSS声明
 */
export const cssRules = {
  'po-ts': {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
  },
  'line-3-ell': {
    'display': '-webkit-box',
    '-webkit-box-orient': 'VERTICAL',
    '-webkit-line-clamp': '3',
    'word-break': 'break-all',
    'overflow': 'hidden',
  },
  'line-2-ell': {
    'display': '-webkit-box',
    '-webkit-box-orient': 'VERTICAL',
    '-webkit-line-clamp': '2',
    'word-break': 'break-all',
    'overflow': 'hidden',
  },
  'line-1-ell': {
    'overflow': 'hidden',
    'text-overflow': 'ellipsis',
    'white-space': 'nowrap',
  },
  'c-primary': {
    color: '#409eff',
  },
  // 全局公共复杂样式balabala...
}
