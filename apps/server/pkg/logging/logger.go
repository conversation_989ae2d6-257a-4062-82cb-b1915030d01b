package logging

import (
	"encoding/json"
	"os"
	"path"

	"mis/pkg/env"

	"go.uber.org/zap"
)

// Logger is the global logger instance.
var Logger *zap.Logger

func Setup() error {
	var logPath string
	outputPaths := []string{}
	errorOutputPaths := []string{}
	if env.ENV != env.LOCAL {
		logPath = "/zkh/log"
	} else {
		logPath = "./logs"
	}
	outputPaths = append(outputPaths, path.Join(logPath, "app.log"))
	errorOutputPaths = append(errorOutputPaths, path.Join(logPath, "error.log"))
	if env.ENV == env.LOCAL {
		outputPaths = append(outputPaths, "stdout")
		errorOutputPaths = append(errorOutputPaths, "stderr")
	}
	// 从配置文件读取配置
	config := map[string]interface{}{
		"level":            "debug",
		"encoding":         "json",
		"outputPaths":      outputPaths,
		"errorOutputPaths": errorOutputPaths,
		"encoderConfig": map[string]string{
			"messageKey":    "message",
			"levelKey":      "level",
			"levelEncoder":  "lowercase",
			"timeKey":       "timestamp",
			"timeEncoder":   "iso8601",
			"callerKey":     "caller",
			"callerEncoder": "short",
		},
	}

	// 确保日志目录存在
	if err := os.MkdirAll(logPath, 0o755); err != nil {
		return err
	}

	// 转换为zap配置
	rawJSON, errJSON := json.Marshal(config)
	if errJSON != nil {
		return errJSON
	}

	var cfg zap.Config
	if errCfg := json.Unmarshal(rawJSON, &cfg); errCfg != nil {
		return errCfg
	}

	// 构建logger
	logger, errLog := cfg.Build(zap.AddCallerSkip(1))
	if errLog != nil {
		return errLog
	}

	Logger = logger
	return nil
}

// Sync flushes any buffered log entries and ensures all logs are written.
func Sync() {
	if Logger != nil {
		Logger.Sync()
	}
}
