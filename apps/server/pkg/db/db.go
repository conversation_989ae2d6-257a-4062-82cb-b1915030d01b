package db

import (
	"mis/pkg/env"
	"os"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

var DB *gorm.DB

func Setup() {
	var err error
	db_url := os.Getenv("DATABASE_URL")
	DB, err = gorm.Open(mysql.Open(db_url))
	if err != nil {
		panic("failed to connect database")
	}

	sqlDB, err := DB.DB()
	if err != nil {
		panic("failed to get underlying *sql.DB")
	}

	sqlDB.SetMaxIdleConns(10)
	sqlDB.SetMaxOpenConns(100)
}

func GetDB() *gorm.DB {
	if env.ENV == "LOCAL" {
		return DB.Debug()
	}
	return DB
}
