package constant

import "mis/pkg/env"

const (
	ClientID     = "union"
	ClientSecret = "dW5pb24zNjA="
)

type Host struct {
	AuthHost     string
	FeHost       string
	BossHost     string
	SecurityHost string
}

var Hosts = Host{
	AuthHost:     "https://auth.zkh360.com",
	FeHost:       "http://api-fe-boss-zkh360-com.zkh-boss:8080",
	BossHost:     "http://boss-zkh360-com.zkh-boss:8080",
	SecurityHost: "http://security-service-zkh360-com.share:8080",
}

func Setup() {
	if env.ENV != env.PRO {
		Hosts.AuthHost = "https://auth-uat.zkh360.com"
		Hosts.FeHost = "https://api-fe-boss-uat.zkh360.com"
		Hosts.BossHost = "https://boss-uat.zkh360.com"
		Hosts.SecurityHost = "https://security-service-uat.zkh360.com"
	}
}
