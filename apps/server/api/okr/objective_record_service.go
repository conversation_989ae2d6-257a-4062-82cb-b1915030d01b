package okr

import (
	"mis/api/okr/models"
	"mis/middleware"
	"mis/pkg/logging"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

func DeleteObjectiveRecords(c *gin.Context) {
	user := middleware.GetUsrInfoFromContext(c)
	logging.Logger.Info("delete records", zap.String("username", user.Username))
	deleteObjectiveByIds(c, models.DelRecordByIds)
}

func UpdateObjectiveRecords(c *gin.Context) {
	var records []*models.ObjectiveRecord
	user := middleware.GetUsrInfoFromContext(c)
	if err := c.ShouldBindJSON(&records); err != nil {
		c.JSON(400, gin.H{
			"error": "无效的请求数据: " + err.Error(),
		})
		return
	}

	for _, record := range records {
		record.Updater = user.Username
		if err := models.UpdateObjectiveWithRecord(record); err != nil {
			c.<PERSON>(500, gin.H{
				"error": "更新失败: " + err.Error(),
			})
			return
		}
	}
	c.J<PERSON>(200, gin.H{
		"data": records,
	})
}

func NewObjectRecord(c *gin.Context) {
	var record models.ObjectiveRecord
	user := middleware.GetUsrInfoFromContext(c)
	if err := c.ShouldBindJSON(&record); err != nil {
		c.JSON(400, gin.H{
			"error": "无效的请求数据: " + err.Error(),
		})
		return
	}
	record.Creator = user.Username
	record.Updater = user.Username
	logging.Logger.Info("create record", zap.Any("record", record))
	if err := models.CreateRecord(&record); err != nil {
		c.JSON(500, gin.H{
			"error": "创建失败: " + err.Error(),
		})
		return
	}
	c.JSON(200, gin.H{
		"data": record,
	})
}
