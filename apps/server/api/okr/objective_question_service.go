package okr

import (
	"mis/api/okr/models"
	"mis/middleware"
	"mis/pkg/logging"
	"strconv"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

func NewObjectRquestion(c *gin.Context) {
	var record models.ObjectiveQuestion
	user := middleware.GetUsrInfoFromContext(c)
	if err := c.ShouldBindJ<PERSON>N(&record); err != nil {
		c.JSON(400, gin.H{
			"error": "无效的请求数据: " + err.Error(),
		})
		return
	}
	record.Creator = user.Username
	record.Updater = user.Username
	logging.Logger.Info("create question", zap.Any("question", record))
	if err := models.CreateQuestion(&record); err != nil {
		c.JSON(500, gin.H{
			"error": "创建失败: " + err.Error(),
		})
		return
	}
	c.JSO<PERSON>(200, gin.H{
		"data": record,
	})
}

func UpdateObjectiveQuestions(c *gin.Context) {
	var questions []*models.ObjectiveQuestion
	user := middleware.GetUsrInfoFromContext(c)
	if err := c.ShouldBindJSON(&questions); err != nil {
		c.JSON(400, gin.H{
			"error": "无效的请求数据: " + err.Error(),
		})
		return
	}

	for _, question := range questions {
		question.Updater = user.Username
		if err := models.UpdateObjectiveWithQuestion(question); err != nil {
			c.JSON(500, gin.H{
				"error": "更新失败: " + err.Error(),
			})
			return
		}
	}
	c.JSON(200, gin.H{
		"data": questions,
	})
}

func DeleteObjectiveQuestions(c *gin.Context) {
	user := middleware.GetUsrInfoFromContext(c)
	logging.Logger.Info("delete questions", zap.String("username", user.Username))
	deleteObjectiveByIds(c, models.DelQuestionByIds)
}

func CreateTaskRquestion(c *gin.Context) {
	user := middleware.GetUsrInfoFromContext(c)
	username := user.Username
	taskIdStr := c.Param("id")
	taskId, err := strconv.Atoi(taskIdStr)
	if err != nil {
		c.JSON(500, gin.H{"error": err.Error()})
		return
	}

	var record models.ObjectiveQuestion
	record.Creator = username
	record.Updater = username
	if err := c.ShouldBindJSON(&record); err != nil {
		c.JSON(400, gin.H{
			"error": "无效的请求数据: " + err.Error(),
		})
		return
	}
	if err := models.CreateTaskQuestion(taskId, &record); err != nil {
		c.JSON(500, gin.H{
			"error": "创建失败: " + err.Error(),
		})
		return
	}
	c.JSON(200, gin.H{
		"data": record,
	})
}
