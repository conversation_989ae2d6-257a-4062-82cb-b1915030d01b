package models

import "mis/pkg/db"

type ObjectiveRecord struct {
	ID          int    `json:"id" gorm:"column:id;primaryKey"`
	ObjectiveID int    `json:"objective_id" gorm:"column:objective_id;type:int:not null"`
	Date        string `json:"date" gorm:"column:date;type:date;not null"`
	Value       string `json:"value" gorm:"column:value;type:varchar;not null"`
	Type        int    `json:"type" gorm:"column:type;type:tinyint;not null"`
	IsDeleted   int    `json:"is_deleted" gorm:"column:is_deleted;type:tinyint"`
	Creator     string `json:"creator" gorm:"column:creator;type:varchar;not null"`
	Updater     string `json:"updater" gorm:"column:updater;type:varchar;not null"`
}

func (ObjectiveRecord) TableName() string {
	return "fe_mis_objective_record"
}

func CreateRecord(obj *ObjectiveRecord) error {
	return db.DB.Create(obj).Error
}

func UpdateObjectiveWithRecord(obj *ObjectiveRecord) error {
	return db.DB.Save(obj).Error
}

func DelRecordByIds(ids []int) error {
	var records []*ObjectiveRecord
	err := db.DB.Where("id in ?", ids).Find(&records).Update("is_deleted", 1).Error
	if err != nil {
		return err
	}
	return nil
}
