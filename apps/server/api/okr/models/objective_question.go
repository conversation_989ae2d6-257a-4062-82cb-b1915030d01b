package models

import (
	"mis/pkg/db"
)

type ObjectiveQuestion struct {
	ID             int                `json:"id" gorm:"column:id;primaryKey"`
	ObjectiveID    int                `json:"objective_id" gorm:"column:objective_id;type:int:not null"`
	Question       string             `json:"question" gorm:"column:question;type:varchar;not null"`
	TaskID         int                `json:"task_id" gorm:"column:task_id;type:int;default:null"`
	IsDeleted      int                `json:"is_deleted" gorm:"column:is_deleted;type:tinyint"`
	Creator        string             `json:"creator" gorm:"column:creator;type:varchar;not null"`
	Updater        string             `json:"updater" gorm:"column:updater;type:varchar;not null"`
	CreateDatetime string             `json:"gmt_create" gorm:"column:gmt_create;type:datetime;default:null"`
	UpdateDatetime string             `json:"gmt_modified" gorm:"column:gmt_modified;type:datetime;default:null"`
	Objective      Objective          `json:"objective" gorm:"foreignKey:objective_id"`
	Answers        []*ObjectiveAnswer `json:"answers" gorm:"foreignKey:QuestionID"`
}

func (ObjectiveQuestion) TableName() string {
	return "fe_mis_objective_question"
}

func CreateQuestion(obj *ObjectiveQuestion) error {
	return db.DB.Create(obj).Error
}

func UpdateObjectiveWithQuestion(obj *ObjectiveQuestion) error {
	return db.DB.Save(obj).Error
}

func DelQuestionByIds(ids []int) error {
	var questions []*ObjectiveQuestion
	err := db.DB.Where("id in ?", ids).Find(&questions).Update("is_deleted", 1).Error
	if err != nil {
		return err
	}
	return nil
}

func CreateTaskQuestion(taskId int, obj *ObjectiveQuestion) error {
	obj.TaskID = taskId
	return db.DB.Create(obj).Error
}
