package models

import (
	"mis/pkg/db"
)

type ObjectiveAnswer struct {
	ID             int    `json:"id" gorm:"column:id;primaryKey"`
	ObjectiveID    int    `json:"objective_id" gorm:"column:objective_id;type:int:not null"`
	QuestionID     int    `json:"question_id" gorm:"column:question_id;type:int:not null"`
	TaskID         int    `json:"task_id" gorm:"column:task_id;type:int:not null"`
	Content        string `json:"content" gorm:"column:content;type:varchar;not null"`
	IsDeleted      int    `json:"is_deleted" gorm:"column:is_deleted;type:tinyint"`
	Files          string `json:"files" gorm:"column:files;type:varchar"`
	Creator        string `json:"creator" gorm:"column:creator;type:varchar;not null"`
	Updater        string `json:"updater" gorm:"column:updater;type:varchar;not null"`
	Username       string `json:"username" gorm:"column:username;type:varchar;not null"`
	Nickname       string `json:"nickname" gorm:"column:nickname;type:varchar;not null"`
	CreateDatetime string `json:"gmt_create" gorm:"column:gmt_create;type:datetime;default:null"`
	UpdateDatetime string `json:"gmt_modified" gorm:"column:gmt_modified;type:datetime;default:null"`
}

func (ObjectiveAnswer) TableName() string {
	return "fe_mis_objective_answer"
}

// SaveObjectiveAnswer 保存 ObjectiveAnswer 记录
func SaveObjectiveAnswer(obj *ObjectiveAnswer) error {
	return db.DB.Create(obj).Error
}

// 保存多个 ObjectiveAnswer 记录
func SaveObjectiveAnswers(answers []*ObjectiveAnswer) error {
	if len(answers) == 0 {
		return nil
	}
	tx := db.DB.Begin()
	if tx.Error != nil {
		return tx.Error
	}
	for _, answer := range answers {
		if err := tx.Create(answer).Error; err != nil {
			tx.Rollback()
			return err
		}
	}
	return tx.Commit().Error
}

// GetObjectiveAnswerByJobID 根据 JobID 获取 ObjectiveAnswer 记录
func GetObjectiveAnswerByQuestionIDAndObjIdInDB(questionID int, objId int, taskId int) ([]*ObjectiveAnswer, error) {
	var answers []*ObjectiveAnswer
	err := db.DB.Where("question_id = ? AND objective_id = ? AND task_id = ? AND (is_deleted != 1 OR is_deleted IS NULL)", questionID, objId, taskId).Find(&answers).Error
	if err != nil {
		return nil, err
	}
	return answers, nil
}

// 定义新的结构体来封装返回结果
type AnswerWithObjectiveAndQuestions struct {
	Answer   *ObjectiveAnswer   `json:"answer"`
	OKRName  string             `json:"okr_name"`
	Question *ObjectiveQuestion `json:"question"`
	USERName string             `json:"user_name"`
	TaskId   int                `json:"task_id"`
}

// 修改函数签名，添加 taskId 参数
func GetAllObjectiveAnswersByTaskId(taskId int, page int, pageSize int) ([]*AnswerWithObjectiveAndQuestions, int64, error) {
	// var answers []*ObjectiveAnswer
	// var total int64
	// var jobIDs []int

	// // 先根据 taskId 查询 Job 表获取对应的 Job ID
	// if taskId != 0 {
	// 	var jobs []Job
	// 	err := db.DB.Where("task_id = ?", taskId).Find(&jobs).Error
	// 	if err != nil {
	// 		return nil, 0, err
	// 	}
	// 	for _, job := range jobs {
	// 		jobIDs = append(jobIDs, job.ID)
	// 	}
	// 	// 如果 taskId 有值但没查到对应的 jobId，直接返回空结果
	// 	if len(jobIDs) == 0 {
	// 		return []*AnswerWithObjectiveAndQuestions{}, 0, nil
	// 	}
	// }

	// // 构建查询条件
	// query := db.DB
	// // 默认添加未删除条件
	// query = query.Where("is_deleted != 1 OR is_deleted IS NULL")

	// if len(jobIDs) > 0 {
	// 	query = query.Where("job_id IN (?)", jobIDs)
	// } else if jobId != 0 {
	// 	query = query.Where("job_id = ?", jobId)
	// }

	// // 查询总数
	// if err := query.Model(&ObjectiveAnswer{}).Count(&total).Error; err != nil {
	// 	return nil, 0, err
	// }

	// // 计算偏移量
	// offset := (page - 1) * pageSize
	// // 执行分页查询
	// if err := query.Order("gmt_create DESC").Offset(offset).Limit(pageSize).Find(&answers).Error; err != nil {
	// 	return nil, 0, err
	// }

	// if len(answers) == 0 {
	// 	return []*AnswerWithObjectiveAndQuestions{}, total, nil
	// }

	// // 批量获取 Objective 数据
	// objectiveIDs := make([]int, 0, len(answers))
	// for _, answer := range answers {
	// 	objectiveIDs = append(objectiveIDs, answer.ObjectiveID)
	// }
	// objectivesMap := make(map[int]Objective)
	// var objectives []Objective
	// if err := db.DB.Where("id IN (?)", objectiveIDs).Find(&objectives).Error; err != nil {
	// 	return nil, 0, err
	// }
	// for _, obj := range objectives {
	// 	objectivesMap[obj.ID] = obj
	// }

	// // 批量获取 ObjectiveQuestion 数据
	// questionIDs := make([]int, 0, len(answers))
	// for _, answer := range answers {
	// 	questionIDs = append(questionIDs, answer.QuestionID)
	// }
	// questionsMap := make(map[int]*ObjectiveQuestion)
	// var questions []ObjectiveQuestion
	// if err := db.DB.Where("id IN (?)", questionIDs).Find(&questions).Error; err != nil {
	// 	return nil, 0, err
	// }
	// for i := range questions {
	// 	questionsMap[questions[i].ID] = &questions[i]
	// }

	// // 批量获取 Job 数据
	// jobIDsForUser := make([]int, 0, len(answers))
	// for _, answer := range answers {
	// 	jobIDsForUser = append(jobIDsForUser, answer.JobID)
	// }
	// jobsMap := make(map[int]*Job)
	// var jobs []Job
	// if err := db.DB.Where("id IN (?)", jobIDsForUser).Find(&jobs).Error; err != nil {
	// 	return nil, 0, err
	// }
	// for i := range jobs {
	// 	jobsMap[jobs[i].ID] = &jobs[i]
	// }

	// var result []*AnswerWithObjectiveAndQuestions
	// for _, answer := range answers {
	// 	objective, ok := objectivesMap[answer.ObjectiveID]
	// 	if !ok {
	// 		continue
	// 	}

	// 	question, ok := questionsMap[answer.QuestionID]
	// 	if !ok {
	// 		continue
	// 	}

	// 	user, ok := jobsMap[answer.JobID]
	// 	if !ok {
	// 		continue
	// 	}

	// 	result = append(result, &AnswerWithObjectiveAndQuestions{
	// 		Answer:   answer,
	// 		OKRName:  objective.Name,
	// 		Question: question,
	// 		USERName: user.Username,
	// 		TaskId:   user.TaskId,
	// 	})
	// }

	return nil, 0, nil
}

// 删除答案
func DelObjectiveAnswerByIds(ids []int) error {
	return db.DB.Model(&ObjectiveAnswer{}).Where("id IN (?)", ids).Update("is_deleted", 1).Error
}

// 批量更新答案
func UpdateObjectiveAnswers(answers []*ObjectiveAnswer) error {
	tx := db.DB.Begin()
	if tx.Error != nil {
		return tx.Error
	}

	for _, answer := range answers {

		updateMap := make(map[string]interface{})

		if answer.Content != "" {
			updateMap["content"] = answer.Content
		}
		if answer.Files != "" {
			updateMap["files"] = answer.Files
		}
		if answer.Updater != "" {
			updateMap["updater"] = answer.Updater
		}
		if answer.Username != "" {
			updateMap["username"] = answer.Username
		}
		if answer.Nickname != "" {
			updateMap["nickname"] = answer.Nickname
		}
		if answer.UpdateDatetime != "" {
			updateMap["gmt_modified"] = answer.UpdateDatetime
		}

		if len(updateMap) > 0 {
			err := tx.Model(&ObjectiveAnswer{}).
				Where("id = ?", answer.ID).
				Updates(updateMap).Error

			if err != nil {
				tx.Rollback()
				return err
			}
		}
	}

	return tx.Commit().Error
}
