package models

import (
	"mis/pkg/db"

	"gorm.io/gorm"
)

type ObjectiveUser struct {
	ID          int       `json:"id" gorm:"column:id;primaryKey"`
	ObjectiveID int       `json:"objective_id" gorm:"column:objective_id;type:int:not null"`
	Type        int       `json:"type" gorm:"column:type;type:tinyint;not null"`
	Username    string    `json:"username" gorm:"column:username;type:varchar;not null"`
	Nickname    string    `json:"nickname" gorm:"column:nickname;type:varchar;not null"`
	IsDeleted   int       `json:"is_deleted" gorm:"column:is_deleted;type:tinyint"`
	Objective   Objective `json:"objective" gorm:"foreignKey:ObjectiveID"`
	Creator     string    `json:"creator" gorm:"column:creator;type:varchar;not null"`
	Updater     string    `json:"updater" gorm:"column:updater;type:varchar;not null"`
}

func (ObjectiveUser) TableName() string {
	return "fe_mis_objective_user"
}

func CreateUser(obj *ObjectiveUser) error {
	return db.DB.Create(obj).Error
}

func UpdateObjectiveWithUser(obj *ObjectiveUser) error {
	return db.DB.Save(obj).Error
}

func DelUserByIds(ids []int) error {
	var users []*ObjectiveUser
	err := db.DB.Where("id in ?", ids).Find(&users).Update("is_deleted", 1).Error
	if err != nil {
		return err
	}
	return nil
}

// GetAllUsers 获取所有的 ObjectiveUser 记录
// func GetAllUsers(admin string) ([]*ObjectiveUser, error) {
// 	var users []*ObjectiveUser
// 	// 使用 DISTINCT 确保用户不重复，JOIN 关联 OKR 表和用户表
// 	err := db.DB.Table("fe_pm_objective_user").
// 		Select("DISTINCT fe_pm_objective_user.*").
// 		Joins("JOIN fe_pm_objective ON fe_pm_objective.id = fe_pm_objective_user.objective_id").
// 		Where("fe_pm_objective.is_deleted != 1 OR fe_pm_objective.is_deleted IS NULL").
// 		Where("fe_pm_objective.admin = ?", admin).
// 		Where("fe_pm_objective_user.is_deleted != 1 OR fe_pm_objective_user.is_deleted IS NULL").
// 		Find(&users).Error
// 	if err != nil && err != gorm.ErrRecordNotFound {
// 		return nil, err
// 	}
// 	return users, nil
// }

func ListUserByObjectiveAdmin(admin string, year string) ([]*ObjectiveUser, error) {
	var users []*ObjectiveUser

	var adminObjIds []int
	errAdmin := db.GetDB().Model(&ObjectiveUser{}).Select("objective_id").Where("type = 0 AND username =?", admin).Where("is_deleted!= 1 OR is_deleted IS NULL").Pluck("objective_id", &adminObjIds).Error
	if errAdmin != nil {
		return nil, errAdmin
	}
	var objectiveIds []int
	errObj := db.GetDB().Model(&Objective{}).Select("id").Where("id in ?", adminObjIds).Where("year = ?", year).Where("is_deleted!= 1 OR is_deleted IS NULL").Pluck("id", &objectiveIds).Error

	if errObj != nil {
		return nil, errObj
	}
	errUser := db.GetDB().Where("objective_id in ?", objectiveIds).Where("type != 0").Where("is_deleted!= 1 OR is_deleted IS NULL").Find(&users).Error

	if errUser != nil && errUser != gorm.ErrRecordNotFound {
		return nil, errUser
	}
	return users, nil
}
