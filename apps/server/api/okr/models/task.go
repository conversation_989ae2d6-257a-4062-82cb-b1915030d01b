package models

import (
	"mis/pkg/db"

	"gorm.io/gorm"
)

type Task struct {
	ID             int                  `json:"id" gorm:"column:id;primaryKey"`
	IsDeleted      int                  `json:"is_deleted" gorm:"column:is_deleted;type:tinyint"`
	Year           int                  `json:"year" gorm:"column:year;type:int;not null"`
	Creator        string               `json:"creator" gorm:"column:creator;type:varchar;not null"`
	Updater        string               `json:"updater" gorm:"column:updater;type:varchar;not null"`
	CreateDatetime string               `json:"gmt_create" gorm:"column:gmt_create;type:datetime;default:null"`
	UpdateDatetime string               `json:"gmt_modified" gorm:"column:gmt_modified;type:datetime;default:null"`
	Questions      []*ObjectiveQuestion `json:"questions" gorm:"foreignKey:task_id"`
	AiResults      []*AiResult          `json:"ai_results" gorm:"foreignKey:task_id"`
}

func (Task) TableName() string {
	return "fe_mis_objective_task"
}

func SaveTask(obj *Task) error {
	return db.DB.Create(obj).Error
}

// GetAllTasks 获取所有未被删除的任务列表，支持分页，并返回总数
// 修改函数签名，添加 taskId 参数
func GetAllTasks(username string, page, pageSize, taskId int) ([]Task, int64, error) {
	var tasks []Task
	var total int64
	DB := db.GetDB()
	// 构建查询对象
	query := DB.Model(&Task{}).Where("is_deleted = ?", 0).Where("creator =?", username)

	// 如果 taskId 不为 0，添加 taskId 查询条件
	if taskId != 0 {
		query = query.Where("id = ?", taskId)
	}

	// 先查询总数
	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// 计算偏移量
	offset := (page - 1) * pageSize
	// 按照 CreateDatetime 字段倒序排序，并添加分页查询
	err = query.Preload("Questions", func(db *gorm.DB) *gorm.DB {
		return db.Where("question != ''").Where("is_deleted != 1 OR is_deleted IS NULL").
			Preload("Objective").Where("is_deleted != 1 OR is_deleted IS NULL").
			Preload("Answers").Where("is_deleted != 1 OR is_deleted IS NULL")
	}).Preload("AiResults", func(db *gorm.DB) *gorm.DB {
		return db.Where("is_deleted != 1 OR is_deleted IS NULL")
	}).Order("gmt_create DESC").Offset(offset).Limit(pageSize).Find(&tasks).Error

	return tasks, total, err
}

// DeleteTask 根据任务 ID 删除任务，实际是标记为已删除
func DeleteTaskByIds(taskID []int) error {
	// 更新任务的 IsDeleted 字段为 1 表示已删除
	updateData := map[string]interface{}{
		"is_deleted": 1,
	}
	return db.DB.Model(&Task{}).Where("id IN ?", taskID).Updates(updateData).Error
}
