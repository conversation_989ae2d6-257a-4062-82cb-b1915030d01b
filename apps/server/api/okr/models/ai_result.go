package models

import "mis/pkg/db"

type AiResult struct {
	ID             int    `json:"id" gorm:"column:id;primaryKey"`
	IsDeleted      int    `json:"is_deleted" gorm:"column:is_deleted;type:tinyint"`
	TaskId         int    `json:"task_id" gorm:"column:task_id;primaryKey"`
	ObjectiveID    int    `json:"objective_id" gorm:"column:objective_id;type:int:not null"`
	Risk           string `json:"risk" gorm:"column:risk;type:varchar;not null"`
	Suggestion     string `json:"suggestion" gorm:"column:suggestion;type:varchar;not null"`
	CreateDatetime string `json:"gmt_create" gorm:"column:gmt_create;type:datetime;default:null"`
	UpdateDatetime string `json:"gmt_modified" gorm:"column:gmt_modified;type:datetime;default:null"`
}

func (AiResult) TableName() string {
	return "fe_mis_objective_ai_result"
}

func SaveAiResultToDB(obj *AiResult) error {
	return db.DB.Create(obj).Error
}

func GetAiResultByTaskId(objectiveId int, taskId int) (*AiResult, error) {
	var result AiResult
	var results []AiResult
	err := db.DB.Where("objective_id = ? AND task_id = ? AND (is_deleted != 1 OR is_deleted IS NULL)", objectiveId, taskId).
		Order("gmt_create DESC").
		Find(&results).Error

	if err != nil {
		return nil, err
	}

	// 检查是否找到结果
	if len(results) == 0 {
		return nil, nil
	}

	// 返回第一个结果
	result = results[0]
	return &result, nil
}

func BatchGetAiResults(taskId int, objectiveIds []int) ([]*AiResult, error) {
	DB := db.GetDB()
	var results []*AiResult

	err := DB.Where("task_id = ? AND objective_id IN (?)", taskId, objectiveIds).
		Where("is_deleted != 1 OR is_deleted IS NULL").
		Find(&results).Error

	if err != nil {
		return nil, err
	}

	return results, nil
}

func DeleteAiResultByIds(ids []int) error {
	// 更新任务的 IsDeleted 字段为 1 表示已删除
	updateData := map[string]interface{}{
		"is_deleted": 1,
	}
	return db.DB.Model(&Task{}).Where("id IN ?", ids).Updates(updateData).Error
}
