package models

import "mis/pkg/db"

type AiAnalysis struct {
	ID             int    `json:"id" gorm:"column:id;primaryKey"`
	IsDeleted      int    `json:"is_deleted" gorm:"column:is_deleted;type:tinyint"`
	TaskId         int    `json:"task_id" gorm:"column:task_id;primaryKey"`
	NextPriorities string `json:"next_priorities" gorm:"column:next_priorities;type:varchar;not null"`
	PastHighlights string `json:"past_highlights" gorm:"column:past_highlights;type:varchar;not null"`
	Username       string `json:"username" gorm:"column:username;type:varchar;not null"`
	CreateDatetime string `json:"gmt_create" gorm:"column:gmt_create;type:datetime;default:null"`
	UpdateDatetime string `json:"gmt_modified" gorm:"column:gmt_modified;type:datetime;default:null"`
}

func (AiAnalysis) TableName() string {
	return "fe_mis_objective_ai_analysis"
}

func SaveAiAnalysisToDB(obj *AiAnalysis) error {
	if err := db.DB.Create(obj).Error; err != nil {
		return err
	}
	// 重新查询以获取数据库生成的时间
	return db.DB.First(obj, obj.ID).Error
}

func GetAiAnalysisByTaskId(taskId int, username string) (*AiAnalysis, error) {
	var result AiAnalysis
	var results []AiAnalysis
	err := db.DB.Where("task_id = ? AND username = ? AND (is_deleted != 1 OR is_deleted IS NULL)", taskId, username).
		Order("gmt_create DESC").
		Find(&results).Error

	if err != nil {
		return nil, err
	}

	// 检查是否找到结果
	if len(results) == 0 {
		return nil, nil
	}

	// 返回第一个结果
	result = results[0]
	return &result, nil
}

func GetAiAnalysisHistory(username string) ([]AiAnalysis, error) {
	var results []AiAnalysis

	query := db.DB.Where("username = ? AND is_deleted != 1 OR is_deleted IS NULL", username)
	err := query.Order("gmt_create DESC").Find(&results).Error

	return results, err
}
