package models

import (
	"mis/pkg/db"

	"gorm.io/gorm"
)

type Objective struct {
	ID             int                  `json:"id" gorm:"column:id;primaryKey"`
	Tier1          string               `json:"tier1" gorm:"column:tier1;type:varchar:not null"`
	Tier2          string               `json:"tier2" gorm:"column:tier2;type:varchar;not null"`
	Name           string               `json:"name" gorm:"column:name;type:varchar;not null"`
	Action         string               `json:"action" gorm:"column:action;type:varchar;not null"`
	Unit           int                  `json:"unit" gorm:"column:unit;type:tinyint;not null"`
	Year           int                  `json:"year" gorm:"column:year;type:int;not null"`
	IsDeleted      int                  `json:"is_deleted" gorm:"column:is_deleted;type:tinyint"`
	CreateDatetime string               `json:"gmt_create" gorm:"column:gmt_create;type:datetime;default:null"`
	UpdateDatetime string               `json:"gmt_modified" gorm:"column:gmt_modified;type:datetime;default:null"`
	Creator        string               `json:"creator" gorm:"column:creator;type:varchar;not null"`
	Updater        string               `json:"updater" gorm:"column:updater;type:varchar;not null"`
	Users          []*ObjectiveUser     `json:"users" gorm:"foreignKey:ObjectiveID"`
	Questions      []*ObjectiveQuestion `json:"questions" gorm:"foreignKey:ObjectiveID"`
	Records        []*ObjectiveRecord   `json:"records" gorm:"foreignKey:ObjectiveID"`
}

type StaffUser struct {
	Name     string `json:"name"`
	Nickname string `json:"nickname"`
}

type ObjectiveWithAdmin struct {
	Objective
	Admin StaffUser `json:"admin"`
}

func (Objective) TableName() string {
	return "fe_mis_objective"
}

func Create(obj *ObjectiveWithAdmin) error {
	tx := db.DB.Begin()
	if tx.Error != nil {
		return tx.Error
	}
	username := obj.Admin.Name
	nickname := obj.Admin.Nickname
	objective := obj.Objective
	if err1 := db.DB.Create(&objective).Error; err1 != nil {
		return err1
	}
	user := ObjectiveUser{
		Username:    username,
		Nickname:    nickname,
		Type:        0,
		ObjectiveID: objective.ID,
	}
	if err2 := db.DB.Create(&user).Error; err2 != nil {
		tx.Rollback()
		return err2
	}
	return nil
}

func DelByIds(ids []int) error {
	var objectives []*Objective
	err := db.DB.Where("id in ?", ids).Find(&objectives).Update("is_deleted", 1).Error
	if err != nil {
		return err
	}
	return nil
}

func Update(obj *Objective) error {
	tx := db.DB.Begin()
	if tx.Error != nil {
		return tx.Error
	}
	updateData := map[string]interface{}{
		"tier1":   obj.Tier1,
		"tier2":   obj.Tier2,
		"name":    obj.Name,
		"action":  obj.Action,
		"unit":    obj.Unit,
		"year":    obj.Year,
		"updater": obj.Updater,
	}
	result := tx.Model(obj).Where("id = ?", obj.ID).Updates(updateData)
	if result.Error != nil {
		tx.Rollback()
		return result.Error
	}
	return tx.Commit().Error
}

func getTxByAdminAndUser(admin string, name string, username string, year int) (*gorm.DB, string, []interface{}) {
	DB := db.GetDB()
	tx := DB.Select("DISTINCT fe_mis_objective.*").Joins("JOIN fe_mis_objective_user admins ON fe_mis_objective.id = admins.objective_id")
	var conditions []interface{}
	sql := "fe_mis_objective.is_deleted = 0 AND admins.is_deleted = 0 AND admins.type = 0 AND admins.username =?"
	conditions = append(conditions, admin)

	if name != "" {
		// 修改为模糊查询
		sql += " AND name LIKE ?"
		conditions = append(conditions, "%"+name+"%")
	}

	if username != "" {
		tx.Joins("JOIN fe_mis_objective_user users ON fe_mis_objective.id = users.objective_id")
		sql += " AND users.is_deleted = 0 AND users.type = 1 AND users.username =?"
		// 添加关联查询 ObjectiveUser 表的条件
		// sql += " AND EXISTS (SELECT 1 FROM fe_pm_objective_user WHERE fe_pm_objective_user.objective_id = fe_pm_objective.id AND fe_pm_objective_user.username = ? AND (fe_pm_objective_user.is_deleted != 1 OR fe_pm_objective_user.is_deleted IS NULL))"
		conditions = append(conditions, username)
	}
	if year != 0 {
		sql += " AND year = ?"
		conditions = append(conditions, year)
	}
	return tx, sql, conditions
}

// GetAllObjective 获取所有未删除的 Objective 记录，支持分页查询并返回总数
func GetAllObjective(admin string, page, pageSize int, name string, username string, year int) ([]*Objective, int64, error) {
	var objectives []*Objective
	var total int64

	// 先查询满足条件的记录总数
	tx1, sql1, conditions1 := getTxByAdminAndUser(admin, name, username, year)
	err := tx1.Model(&Objective{}).Where(sql1, conditions1...).Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// 计算偏移量
	offset := (page - 1) * pageSize

	// 进行分页查询
	tx2, sql2, conditions2 := getTxByAdminAndUser(admin, name, username, year)
	err = tx2.Preload("Users", func(db *gorm.DB) *gorm.DB {
		return db.Where("is_deleted != 1 OR is_deleted IS NULL")
	}).Preload("Questions", func(db *gorm.DB) *gorm.DB {
		return db.Where("is_deleted != 1 OR is_deleted IS NULL").Where("task_id = ? OR task_id IS NULL", "")
	}).Preload("Records", func(db *gorm.DB) *gorm.DB {
		return db.Where("is_deleted != 1 OR is_deleted IS NULL")
	}).Where(sql2, conditions2...).Offset(offset).Limit(pageSize).Order("id DESC").Find(&objectives).Error

	if err != nil && err != gorm.ErrRecordNotFound {
		return nil, 0, err
	}
	return objectives, total, nil
}

func GetById(id int) (*Objective, error) {
	var objective Objective
	err := db.DB.Where("id = ?", id).First(&objective).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return nil, err
	}
	return &objective, nil
}

// type ObjectiveWithJobID struct {
// 	Objective
// 	JobID int `json:"job_id"`
// }

// 通过用户名获取OKR
func GetObjectiveByUsername(username string, taskId int, userType int) ([]*Objective, error) {
	var objectives []*Objective
	DB := db.GetDB()
	err := DB.
		Select("DISTINCT fe_mis_objective.*").
		Joins("JOIN fe_mis_objective_user users ON fe_mis_objective.id = users.objective_id").
		Where("users.username = ? AND users.type = ? AND users.is_deleted != 1", username, userType).
		Preload("Users", func(db *gorm.DB) *gorm.DB {
			return db.Where("is_deleted != 1 OR is_deleted IS NULL")
		}).
		Preload("Questions", func(db *gorm.DB) *gorm.DB {
			return db.Order("gmt_create DESC").
				Where("is_deleted != 1 OR is_deleted IS NULL").
				Where("task_id = ? OR task_id IS NULL OR task_id = 0", taskId)
		}).
		Preload("Records", func(db *gorm.DB) *gorm.DB {
			return db.Where("is_deleted != 1 OR is_deleted IS NULL")
		}).
		Where("fe_mis_objective.is_deleted != 1 OR fe_mis_objective.is_deleted IS NULL").
		Find(&objectives).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return nil, err
	}
	return objectives, nil
}

func ListObjectiveQAWithTaskId(username string, taskId int, userType int) ([]*Objective, error) {
	var objectives []*Objective
	DB := db.GetDB()
	err := DB.
		Select("DISTINCT fe_mis_objective.*").
		Joins("JOIN fe_mis_objective_user users ON fe_mis_objective.id = users.objective_id").
		Where("users.username = ? AND users.type = ? AND users.is_deleted != 1", username, userType).
		Joins("JOIN fe_mis_objective_task task ON task.year = fe_mis_objective.year").
		Where("task.id = ?", taskId).
		Preload("Users", func(db *gorm.DB) *gorm.DB {
			return db.Where("is_deleted != 1 OR is_deleted IS NULL")
		}).
		Preload("Questions", func(db *gorm.DB) *gorm.DB {
			return db.Where("question != ''").Where("is_deleted != 1 OR is_deleted IS NULL").
				Preload("Answers", func(db *gorm.DB) *gorm.DB {
					tx := db.Where("is_deleted != 1 OR is_deleted IS NULL").
						Where("task_id =? OR task_id is NULL OR task_id = 0", taskId)
					if userType == 1 {
						tx = tx.Where("username = ?", username)
					}
					return tx
				}).
				Order("gmt_create ASC").
				Where("task_id =? OR task_id is NULL OR task_id = 0", taskId).
				Where("is_deleted != 1 OR is_deleted IS NULL")
		}).
		Preload("Records", func(db *gorm.DB) *gorm.DB {
			return db.Where("is_deleted != 1 OR is_deleted IS NULL")
		}).
		Where("fe_mis_objective.is_deleted != 1 OR fe_mis_objective.is_deleted IS NULL").
		Find(&objectives).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return nil, err
	}
	return objectives, nil
}

func GetObjectiveWithAiTx(username string) *gorm.DB {
	DB := db.GetDB()
	return DB.Select("DISTINCT fe_mis_objective.*").
		Joins("JOIN fe_mis_objective_user users ON fe_mis_objective.id = users.objective_id").
		Where("users.username = ? AND users.type = ? AND users.is_deleted != 1", username, 0).
		Preload("Users", func(db *gorm.DB) *gorm.DB {
			return db.Where("is_deleted != 1 OR is_deleted IS NULL")
		}).
		Preload("Questions", func(db *gorm.DB) *gorm.DB {
			return db.Preload("Answers", func(db *gorm.DB) *gorm.DB {
				return db.Where("is_deleted != 1 OR is_deleted IS NULL")
			}).Where("is_deleted != 1 OR is_deleted IS NULL")
		}).
		Preload("Records", func(db *gorm.DB) *gorm.DB {
			return db.Where("is_deleted != 1 OR is_deleted IS NULL")
		}).
		Where("fe_mis_objective.is_deleted != 1 OR fe_mis_objective.is_deleted IS NULL")
}
func GetObjectiveById(username string, id int) (*Objective, error) {
	var objective Objective
	err := GetObjectiveWithAiTx(username).
		Where("fe_mis_objective.id =?", id).
		Find(&objective).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return nil, err
	}
	return &objective, nil
}

func GetObjectivesByYear(username string, year int) ([]*Objective, error) {
	var objectives []*Objective
	err := GetObjectiveWithAiTx(username).
		Where("year =?", year).
		Find(&objectives).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return nil, err
	}
	return objectives, nil
}

func ListObjectiveByTaskId(taskId int) ([]*Objective, error) {
	var objectives []*Objective
	DB := db.GetDB()
	err := DB.
		Select("DISTINCT fe_mis_objective.*").
		Joins("JOIN fe_mis_objective_user users ON fe_mis_objective.id = users.objective_id").
		Joins("JOIN fe_mis_objective_task task ON task.year = fe_mis_objective.year").
		Where("users.is_deleted != 1").
		// Where("task.creator = users.username AND users.type = 0").
		Where("task.id = ?", taskId).
		Preload("Users", func(db *gorm.DB) *gorm.DB {
			return db.Where("is_deleted != 1 OR is_deleted IS NULL")
		}).
		Preload("Questions", func(db *gorm.DB) *gorm.DB {
			return db.Where("question != ''").Where("is_deleted != 1 OR is_deleted IS NULL").
				Preload("Answers", func(db *gorm.DB) *gorm.DB {
					tx := db.Where("is_deleted != 1 OR is_deleted IS NULL").
						Where("task_id =? OR task_id is NULL OR task_id = 0", taskId)
					return tx
				}).
				Order("gmt_create ASC").
				Where("task_id =? OR task_id is NULL OR task_id = 0", taskId).
				Where("is_deleted != 1 OR is_deleted IS NULL")
		}).
		Preload("Records", func(db *gorm.DB) *gorm.DB {
			return db.Where("is_deleted != 1 OR is_deleted IS NULL")
		}).
		Where("fe_mis_objective.is_deleted != 1 OR fe_mis_objective.is_deleted IS NULL").
		Find(&objectives).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return nil, err
	}
	return objectives, nil
}
