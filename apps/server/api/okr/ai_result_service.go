package okr

import (
	"strconv"
	"strings"

	"mis/api/okr/models"
	"mis/pkg/logging"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

func CreateAIResult(c *gin.Context) {
	var aiResult models.AiResult

	if err := c.ShouldBindJ<PERSON>(&aiResult); err != nil {
		c.JSON(400, gin.H{
			"error": "无效的请求数据: " + err.<PERSON>rror(),
		})
		return
	}
	logging.Logger.Info("save ai result", zap.Any("aiResult", aiResult))
	if err := models.SaveAiResultToDB(&aiResult); err != nil {
		c.JSO<PERSON>(500, gin.H{
			"error": "创建失败: " + err.Error(),
		})
		return
	}
	c.JSON(200, gin.H{
		"data": aiResult,
	})
}

func GetAIResult(c *gin.Context) {
	objectiveIdStr := c.Query("objectiveId")
	taskIdStr := c.Query("taskId")

	if objectiveIdStr == "" {
		c.JSON(400, gin.H{
			"error": "缺少必要参数: objectiveId",
		})
		return
	}

	if taskIdStr == "" {
		c.JSON(400, gin.H{
			"error": "缺少必要参数: taskId",
		})
		return
	}

	// 将字符串转换为整数
	objectiveId, err := strconv.Atoi(objectiveIdStr)
	if err != nil {
		c.JSON(400, gin.H{
			"error": "无效的objectiveId格式: " + err.Error(),
		})
		return
	}

	taskId, err := strconv.Atoi(taskIdStr)
	if err != nil {
		c.JSON(400, gin.H{
			"error": "无效的taskId格式: " + err.Error(),
		})
		return
	}

	result, err := models.GetAiResultByTaskId(objectiveId, taskId)
	if err != nil {
		c.JSON(500, gin.H{
			"error": "查询失败: " + err.Error(),
		})
		return
	}

	c.JSON(200, gin.H{
		"data": result,
	})
}

type BatchGetAIRequest struct {
	TaskID       int   `json:"taskId" binding:"required,gt=0"`
	ObjectiveIDs []int `json:"objectiveIds" binding:"required,min=1,dive,required,gt=0"`
}

func BatchGetAiResults(c *gin.Context) {
	var req BatchGetAIRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(400, gin.H{
			"error": "无效的请求数据: " + err.Error(),
		})
		return
	}

	results, err := models.BatchGetAiResults(req.TaskID, req.ObjectiveIDs)
	if err != nil {
		c.JSON(500, gin.H{"error": err.Error()})
		return
	}

	c.JSON(200, gin.H{
		"data": results,
	})
}

func DeleteAiResult(c *gin.Context) {
	idsStr := c.Query("ids")
	strArr := strings.Split(idsStr, "_")

	var ids []int
	for _, str := range strArr {
		id, err := strconv.Atoi(str)
		if err != nil {
			c.JSON(400, gin.H{
				"error": "无效的 ID 格式: " + err.Error(),
			})
			return
		}
		ids = append(ids, id)
	}

	if err := models.DeleteAiResultByIds(ids); err != nil {
		c.JSON(500, gin.H{
			"error": "删除失败: " + err.Error(),
		})
		return
	}
	c.JSON(200, gin.H{
		"data": ids,
	})
}
