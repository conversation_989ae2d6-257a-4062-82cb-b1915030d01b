package okr

import (
	"mis/api/okr/models"
	"mis/middleware"
	"mis/pkg/logging"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

func CreateTask(c *gin.Context) {
	user := middleware.GetUsrInfoFromContext(c)
	var task models.Task
	if err := c.ShouldBindJSON(&task); err != nil {
		c.JSON(400, gin.H{
			"error": "无效的请求数据: " + err.Error(),
		})
		return
	}
	task.Creator = user.Username
	task.Updater = user.Username
	logging.Logger.Info("start task", zap.Any("task", task))
	if err := models.SaveTask(&task); err != nil {
		c.JSON(500, gin.H{
			"error": "创建失败: " + err.Error(),
		})
		return
	}
	taskId := task.ID
	c.JSON(200, gin.H{
		"data": taskId,
	})
}

// 获取任务列表
func ListTask(c *gin.Context) {
	pageNumStr := c.Query("pageNum")
	pageSizeStr := c.Query("pageSize")
	taskIdStr := c.Query("taskId")
	taskId, err := strconv.Atoi(taskIdStr)
	pageNum, err := strconv.Atoi(pageNumStr)
	if err != nil {
		pageNum = 1
	}
	pageSize, err := strconv.Atoi(pageSizeStr)
	if err != nil {
		pageSize = 10
	}
	user := middleware.GetUsrInfoFromContext(c)
	tasks, total, err := models.GetAllTasks(user.Username, pageNum, pageSize, taskId)
	if err != nil {
		c.JSON(500, gin.H{
			"error": "获取任务列表失败: " + err.Error(),
		})
		return
	}
	c.JSON(200, gin.H{
		"data":  tasks,
		"total": total,
	})
}

// 删除任务
func DeleteTask(c *gin.Context) {
	idsStr := c.Query("ids")
	strArr := strings.Split(idsStr, "_")

	var ids []int
	for _, str := range strArr {
		id, err := strconv.Atoi(str)
		if err != nil {
			c.JSON(400, gin.H{
				"error": "无效的 ID 格式: " + err.Error(),
			})
			return
		}
		ids = append(ids, id)
	}
	user := middleware.GetUsrInfoFromContext(c)
	logging.Logger.Info("delete task", zap.Any("ids", ids), zap.String("username", user.Username))
	if err := models.DeleteTaskByIds(ids); err != nil {
		c.JSON(500, gin.H{
			"error": "删除失败: " + err.Error(),
		})
		return
	}
	c.JSON(200, gin.H{
		"data": ids,
	})
}
