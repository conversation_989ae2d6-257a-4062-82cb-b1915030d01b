package okr

import (
	"mis/api/okr/models"
	"mis/middleware"
	"mis/pkg/logging"
	"strconv"

	"strings"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// CreateObjectiveAnswer 创建一个新的 ObjectiveAnswer 记录
func CreateObjectiveAnswer(c *gin.Context) {
	var answer models.ObjectiveAnswer
	user := middleware.GetUsrInfoFromContext(c)
	username := user.Username
	if err := c.ShouldBindJSON(&answer); err != nil {
		c.JSON(400, gin.H{
			"error": "无效的请求数据: " + err.<PERSON>rror(),
		})
		return
	}
	answer.Creator = username
	answer.Updater = username
	if err := models.SaveObjectiveAnswer(&answer); err != nil {
		c.JSON(500, gin.H{
			"error": "创建失败: " + err.Error(),
		})
	}
	c.<PERSON>(200, gin.H{
		"data": answer,
	})
}

// 保存多个 ObjectiveAnswer 记录
func CreateObjectiveAnswers(c *gin.Context) {
	user := middleware.GetUsrInfoFromContext(c)
	username := user.Username
	// 定义一个包含 answers 字段的结构体来解析请求数据
	type Request struct {
		Answers []models.ObjectiveAnswer `json:"answers"`
	}
	var req Request
	// 解析请求数据
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(400, gin.H{
			"error": "无效的请求数据: " + err.Error(),
		})
		return
	}

	// 将 []ObjectiveAnswer 转换为 []*ObjectiveAnswer
	var answerPtrs []*models.ObjectiveAnswer
	for i := range req.Answers {
		answer := &req.Answers[i]
		answer.Creator = username
		answer.Updater = username
		answerPtrs = append(answerPtrs, &req.Answers[i])
	}

	// 调用 SaveObjectiveAnswers 函数进行批量保存
	if err := models.SaveObjectiveAnswers(answerPtrs); err != nil {
		c.JSON(500, gin.H{
			"error": "创建失败: " + err.Error(),
		})
		return
	}

	c.JSON(200, gin.H{
		"data": req.Answers,
	})
}

// 通过objid和jobid获取答案
func GetObjectiveAnswerByObjIdAndTaskId(c *gin.Context) {
	// 从请求中获取 objId 和 jobId 参数
	objIdStr := c.Query("objId")
	quesIdStr := c.Query("quesId")
	taskIdStr := c.Query("taskId")
	taskId, err := strconv.Atoi(taskIdStr)
	objId, err := strconv.Atoi(objIdStr)
	quesId, err := strconv.Atoi(quesIdStr)

	if objIdStr == "" || quesIdStr == "" {
		c.JSON(400, gin.H{
			"error": "objId 和 quesId 参数缺失",
		})
		return
	}
	// 获取所有
	answers, err := models.GetObjectiveAnswerByQuestionIDAndObjIdInDB(quesId, objId, taskId)
	if err != nil {
		c.JSON(500, gin.H{
			"error": "获取用户列表失败: " + err.Error(),
		})
	}
	c.JSON(200, gin.H{
		"data": answers,
	})
}

// 删除答案
func DeleteObjectiveAnswer(c *gin.Context) {
	idStr := c.Query("ids")
	ids := strings.Split(idStr, "_")
	var idInts []int
	for _, str := range ids {
		id, err := strconv.Atoi(str)
		if err != nil {
			c.JSON(400, gin.H{
				"error": "无效的 ID 格式: " + err.Error(),
			})
			return
		}
		idInts = append(idInts, id)
	}
	user := middleware.GetUsrInfoFromContext(c)
	logging.Logger.Info("delete objective answer", zap.Any("ids", idInts), zap.String("username", user.Username))
	if err := models.DelObjectiveAnswerByIds(idInts); err != nil {
		c.JSON(500, gin.H{
			"error": "删除失败: " + err.Error(),
		})
		return
	}
	c.JSON(200, gin.H{
		"data": idInts,
	})
}

func UpdateObjectiveAnswers(c *gin.Context) {
	user := middleware.GetUsrInfoFromContext(c)
	username := user.Username
	var answers []*models.ObjectiveAnswer
	// 解析请求数据
	if err := c.ShouldBindJSON(&answers); err != nil {
		c.JSON(400, gin.H{"error": "无效的请求数据: " + err.Error()})
		return
	}

	for _, answer := range answers {
		if answer.ID == 0 {
			c.JSON(400, gin.H{"error": "ID 不能为空"})
			return
		}
		answer.Updater = username
	}

	// 调用 SaveObjectiveAnswers 函数进行批量保存
	if err := models.UpdateObjectiveAnswers(answers); err != nil {
		c.JSON(500, gin.H{"error": "更新失败: " + err.Error()})
		return
	}

	c.JSON(200, gin.H{"data": answers})
}