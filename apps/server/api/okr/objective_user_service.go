package okr

import (
	"mis/api/okr/models"
	"mis/middleware"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
)

func NewObjectRUser(c *gin.Context) {
	var record models.ObjectiveUser
	user := middleware.GetUsrInfoFromContext(c)
	if err := c.ShouldBind<PERSON>(&record); err != nil {
		c.JSO<PERSON>(400, gin.H{
			"error": "无效的请求数据: " + err.<PERSON>rror(),
		})
		return
	}
	record.Creator = user.Username
	record.Updater = user.Username
	if err := models.CreateUser(&record); err != nil {
		c.JSON(500, gin.H{
			"error": "创建失败: " + err.<PERSON>rror(),
		})
		return
	}
	c.JSON(200, gin.H{
		"data": record,
	})
}

func UpdateObjectiveUsers(c *gin.Context) {
	var users []*models.ObjectiveUser
	currentUser := middleware.GetUsrInfoFromContext(c)
	if err := c.<PERSON>(&users); err != nil {
		c.<PERSON>(400, gin.H{
			"error": "无效的请求数据: " + err.<PERSON>rror(),
		})
		return
	}

	for _, user := range users {
		user.Updater = currentUser.Username
		if err := models.UpdateObjectiveWithUser(user); err != nil {
			c.JSON(500, gin.H{
				"error": "更新失败: " + err.Error(),
			})
			return
		}
	}
	c.JSON(200, gin.H{
		"data": users,
	})
}

func DeleteObjectiveUsers(c *gin.Context) {
	deleteObjectiveByIds(c, models.DelUserByIds)
}

// 获取所有用户
func UserList(c *gin.Context) {
	year := c.Query("year")
	if year == "" {
		year = strconv.Itoa(time.Now().Year())
	}
	user := middleware.GetUsrInfoFromContext(c)
	users, err := models.ListUserByObjectiveAdmin(user.Username, year)
	if err != nil {
		c.JSON(500, gin.H{"error": err.Error()})
		return
	}
	c.JSON(200, gin.H{
		"data": users,
	})
}
