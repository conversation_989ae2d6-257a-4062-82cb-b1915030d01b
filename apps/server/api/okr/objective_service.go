package okr

import (
	"strconv"
	"strings"

	commonModels "mis/api/common/models"
	"mis/api/okr/models"
	"mis/middleware"
	"mis/pkg/logging"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

func CreateObjective(c *gin.Context) {
	var objective models.ObjectiveWithAdmin
	user := middleware.GetUsrInfoFromContext(c)
	objective.Creator = user.Username
	objective.Updater = user.Username
	if err := c.ShouldBindJSON(&objective); err != nil {
		c.JSON(400, gin.H{
			"error": "无效的请求数据: " + err.Error(),
		})
		return
	}

	if err := models.Create(&objective); err != nil {
		c.JSON(500, gin.H{
			"error": "创建失败: " + err.Error(),
		})
		return
	}
	c.JSON(200, gin.H{
		"data": objective,
	})
}

// 定义一个新的结构体，包含 UnitName 字段
type ObjectiveWithUnitName struct {
	models.Objective
	UnitName string `json:"unit_name"`
}

func ListObjective(c *gin.Context) {
	user := middleware.GetUsrInfoFromContext(c)
	pageNumStr := c.Query("pageNum")
	pageSizeStr := c.Query("pageSize")
	name := c.Query("name")
	username := c.Query("username")
	yearStr := c.Query("year")
	year, err := strconv.Atoi(yearStr)
	pageNum, err := strconv.Atoi(pageNumStr)
	if err != nil {
		pageNum = 1
	}
	pageSize, err := strconv.Atoi(pageSizeStr)
	if err != nil {
		pageSize = 10
	}
	objs, total, err := models.GetAllObjective(user.Username, pageNum, pageSize, name, username, year)
	if err != nil {
		c.JSON(500, gin.H{"error": err.Error()})
		return
	}

	// 创建一个新的切片来存储包含 UnitName 的结果
	var objsWithUnitName []ObjectiveWithUnitName
	for _, obj := range objs {
		if obj == nil {
			continue
		}
		unitName, err := commonModels.GetDictNameByTypeAndValue("unit", obj.Unit)
		if err != nil {
			unitName = ""
		}
		objsWithUnitName = append(objsWithUnitName, ObjectiveWithUnitName{
			Objective: *obj, // Dereference the pointer
			UnitName:  unitName,
		})
	}

	c.JSON(200, gin.H{
		"data":  objsWithUnitName,
		"total": total,
	})
}

func UpdateObjective(c *gin.Context) {
	var objective models.Objective
	user := middleware.GetUsrInfoFromContext(c)
	if err := c.ShouldBindJSON(&objective); err != nil {
		c.JSON(400, gin.H{
			"error": "无效的请求数据: " + err.Error(),
		})
		return
	}
	objective.Updater = user.Username
	if err := models.Update(&objective); err != nil {
		c.JSON(500, gin.H{
			"error": "更新失败: " + err.Error(),
		})
	}
	c.JSON(200, gin.H{
		"data": objective,
	})
}

type DeleteFunc func([]int) error

func deleteObjectiveByIds(c *gin.Context, fn DeleteFunc) {
	idsStr := c.Query("ids")

	strArr := strings.Split(idsStr, "_")

	// 转换为整数数组
	var ids []int
	for _, str := range strArr {
		// 过滤掉空字符串
		if str == "" {
			continue
		}
		id, err := strconv.Atoi(str)
		if err != nil {
			c.JSON(400, gin.H{
				"error": "无效的ID格式: " + err.Error(),
			})
			return
		}
		ids = append(ids, id)
	}
	user := middleware.GetUsrInfoFromContext(c)
	logging.Logger.Info("delete objective", zap.Any("ids", ids), zap.String("username", user.Username))
	if err := fn(ids); err != nil {
		c.JSON(500, gin.H{
			"error": "删除失败: " + err.Error(),
		})
		return
	}
	c.JSON(200, gin.H{
		"data": ids,
	})
}

func DeleteObjectives(c *gin.Context) {
	deleteObjectiveByIds(c, models.DelByIds)
}

func listObjectiveByTaskIdAndUsername(c *gin.Context, username string, taskId int) {
	taskIdStr := c.Query("taskId")
	taskId, err := strconv.Atoi(taskIdStr)
	userTypeStr := c.Query("userType")
	userType, err := strconv.Atoi(userTypeStr)
	if username == "" {
		c.JSON(400, gin.H{
			"error": "用户名不能为空",
		})
		return
	}
	objs, err := models.GetObjectiveByUsername(username, taskId, userType)
	if err != nil {
		c.JSON(500, gin.H{"error": err.Error()})
		return
	}
	c.JSON(200, gin.H{
		"data":    objs,
		"message": "查询成功",
	})
}

// 获取用户的OKR
func GetUserObjective(c *gin.Context) {
	user := middleware.GetUsrInfoFromContext(c)
	username := user.Username
	nickname := user.Nickname
	taskIdStr := c.Query("taskId")
	taskId, err := strconv.Atoi(taskIdStr)
	userTypeStr := c.Query("userType")
	userType, err := strconv.Atoi(userTypeStr)
	if username == "" {
		c.JSON(400, gin.H{
			"error": "用户名不能为空",
		})
		return
	}
	objs, err := models.GetObjectiveByUsername(username, taskId, userType)
	if err != nil {
		c.JSON(500, gin.H{"error": err.Error()})
		return
	}
	c.JSON(200, gin.H{
		"data":     objs,
		"message":  "查询成功",
		"username": username,
		"nickname": nickname,
	})
}

func listTaskObjectiveByUserType(c *gin.Context, userType int) {
	user := middleware.GetUsrInfoFromContext(c)
	username := user.Username
	nickname := user.Nickname
	taskIdStr := c.Param("id")
	taskId, err := strconv.Atoi(taskIdStr)
	if username == "" {
		c.JSON(400, gin.H{
			"error": "用户名不能为空",
		})
		return
	}
	objs, err := models.ListObjectiveQAWithTaskId(username, taskId, userType)
	if err != nil {
		c.JSON(500, gin.H{"error": err.Error()})
		return
	}
	c.JSON(200, gin.H{
		"data":     objs,
		"message":  "查询成功",
		"username": username,
		"nickname": nickname,
	})
}

func ListAdminTaskObjective(c *gin.Context) {
	listTaskObjectiveByUserType(c, 0)
}

func ListUserTaskObjective(c *gin.Context) {
	listTaskObjectiveByUserType(c, 1)
}

func ListTaskObjectiveByUsername(c *gin.Context) {
	taskIdStr := c.Param("id")
	username := c.Param("username")
	taskId, err := strconv.Atoi(taskIdStr)
	if username == "" {
		c.JSON(400, gin.H{
			"error": "用户名不能为空",
		})
		return
	}
	objs, err := models.ListObjectiveQAWithTaskId(username, taskId, 1)
	if err != nil {
		c.JSON(500, gin.H{"error": err.Error()})
		return
	}
	c.JSON(200, gin.H{
		"data":    objs,
		"message": "查询成功",
	})
}

func GetObjectiveById(c *gin.Context) {
	idStr := c.Param("id")
	id, err1 := strconv.Atoi(idStr)
	if err1 != nil {
		c.JSON(500, gin.H{"error": err1.Error()})
		return
	}
	user := middleware.GetUsrInfoFromContext(c)
	username := user.Username
	obj, err := models.GetObjectiveById(username, id)
	if err != nil {
		c.JSON(500, gin.H{"error": err.Error()})
		return
	}
	c.JSON(200, gin.H{
		"data":    obj,
		"message": "查询成功",
	})
}

func GetObjectivesByYear(c *gin.Context) {
	idStr := c.Param("year")
	year, err1 := strconv.Atoi(idStr)
	if err1 != nil {
		c.JSON(500, gin.H{"error": err1.Error()})
		return
	}
	user := middleware.GetUsrInfoFromContext(c)
	username := user.Username
	obj, err := models.GetObjectivesByYear(username, year)
	if err != nil {
		c.JSON(500, gin.H{"error": err.Error()})
		return
	}
	c.JSON(200, gin.H{
		"data":    obj,
		"message": "查询成功",
	})
}

func ListObjectiveByTaskId(c *gin.Context) {
	idStr := c.Param("id")
	id, err1 := strconv.Atoi(idStr)
	if err1 != nil {
		c.JSON(500, gin.H{"error": err1.Error()})
		return
	}
	objs, err := models.ListObjectiveByTaskId(id)
	if err != nil {
		c.JSON(500, gin.H{"error": err.Error()})
		return
	}
	c.JSON(200, gin.H{
		"data":    objs,
		"message": "查询成功",
	})
}
