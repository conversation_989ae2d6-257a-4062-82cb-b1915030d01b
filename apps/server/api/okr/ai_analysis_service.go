package okr

import (
	"mis/api/okr/models"
	"mis/pkg/logging"
	"strconv"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

func CreateAiAnalysis(c *gin.Context) {
	var aiAnalysis models.AiAnalysis

	if err := c.Should<PERSON>ind<PERSON>(&aiAnalysis); err != nil {
		c.JSO<PERSON>(400, gin.H{
			"error": "无效的请求数据: " + err.<PERSON>r(),
		})
		return
	}
	logging.Logger.Info("save ai analysis", zap.Any("aiAnalysis", aiAnalysis))
	if err := models.SaveAiAnalysisToDB(&aiAnalysis); err != nil {
		c.JSO<PERSON>(500, gin.H{
			"error": "创建失败: " + err.<PERSON>rror(),
		})
		return
	}
	c.JSON(200, gin.H{
		"data": aiAnalysis,
	})
}

func GetAiAnalysis(c *gin.Context) {
	taskIdStr := c.Query("taskId")
	username := c.Query("username")

	if taskIdStr == "" {
		c.J<PERSON>(400, gin.H{
			"error": "缺少必要参数: taskId",
		})
		return
	}

	// 将字符串转换为整数
	taskId, err := strconv.Atoi(taskIdStr)
	if err != nil {
		c.JSON(400, gin.H{
			"error": "无效的ID格式: " + err.Error(),
		})
		return
	}

	result, err := models.GetAiAnalysisByTaskId(taskId, username)
	if err != nil {
		c.JSON(500, gin.H{
			"error": "查询失败: " + err.Error(),
		})
		return
	}

	c.JSON(200, gin.H{
		"data": result,
	})
}
func GetAiAnalysisHistory(c *gin.Context) {
	username := c.Query("username")

	if username == "" {
		c.JSON(400, gin.H{
			"error": "缺少必要参数: username",
		})
		return
	}

	result, err := models.GetAiAnalysisHistory(username)
	if err != nil {
		c.JSON(500, gin.H{
			"error": "查询失败: " + err.Error(),
		})
		return
	}

	c.JSON(200, gin.H{
		"data": result,
	})
}
