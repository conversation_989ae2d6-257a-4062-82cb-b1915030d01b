package api

import (
	"mis/api/common"
	"mis/api/okr"

	"mis/middleware"

	"github.com/gin-gonic/gin"
)

func Init(r *gin.Engine) *gin.Engine {
	r.GET("/ping", common.Ping)
	r.GET("/user", middleware.CheckAuth, common.UserInfo)
	r.GET("/token", middleware.CheckAuth, common.Token)
	{
		r.GET("/dict/list", common.ListDict)
		r.POST("/dict", middleware.CheckAuth, common.CreateDict)
		r.PUT("/dict", middleware.CheckAuth, common.UpdateDict)
		r.DELETE("/dict", middleware.CheckAuth, common.DeleteDict)
	}
	{
		pmGroup := r.Group("/okr")
		// 分页查询OKR列表
		pmGroup.GET("/objectives", middleware.CheckAuth, okr.ListObjective)
		// 查询OKR详情
		pmGroup.GET("/objective/:id", middleware.CheckAuth, okr.GetObjectiveById)
		pmGroup.GET("/year/:year/objectives", middleware.CheckAuth, okr.GetObjectivesByYear)
		pmGroup.POST("/objective", middleware.CheckAuth, okr.CreateObjective)
		pmGroup.PUT("/objective", middleware.CheckAuth, okr.UpdateObjective)
		pmGroup.DELETE("/objectives", middleware.CheckAuth, okr.DeleteObjectives)
		// 目标值
		pmGroup.POST("/newRecord", middleware.CheckAuth, okr.NewObjectRecord)
		pmGroup.PUT("/objective/records", middleware.CheckAuth, okr.UpdateObjectiveRecords)
		pmGroup.DELETE("/objective/records", middleware.CheckAuth, okr.DeleteObjectiveRecords)
		// 问题
		pmGroup.POST("/newQuestion", middleware.CheckAuth, okr.NewObjectRquestion)
		pmGroup.DELETE("/objective/questions", middleware.CheckAuth, okr.DeleteObjectiveQuestions)
		pmGroup.PUT("/objective/questions", middleware.CheckAuth, okr.UpdateObjectiveQuestions)
		// 创建动态问题类型
		pmGroup.POST("/task/:id/question", middleware.CheckAuth, okr.CreateTaskRquestion)
		// answer
		pmGroup.POST("/objective/answers", middleware.CheckAuth, okr.CreateObjectiveAnswers)
		pmGroup.GET("/objective/answers/list", middleware.CheckAuth, okr.GetObjectiveAnswerByObjIdAndTaskId)
		pmGroup.DELETE("/objective/answers", middleware.CheckAuth, okr.DeleteObjectiveAnswer)
		// user
		pmGroup.GET("/users", middleware.CheckAuth, okr.UserList)
		pmGroup.POST("/newUser", middleware.CheckAuth, okr.NewObjectRUser)
		pmGroup.PUT("/objective/users", middleware.CheckAuth, okr.UpdateObjectiveUsers)
		pmGroup.DELETE("/objective/users", middleware.CheckAuth, okr.DeleteObjectiveUsers)
		// task
		pmGroup.POST("/task", middleware.CheckAuth, okr.CreateTask)
		pmGroup.GET("/task/list", middleware.CheckAuth, okr.ListTask)
		pmGroup.DELETE("/tasks", middleware.CheckAuth, okr.DeleteTask)
		// ai_result
		pmGroup.GET("/aiResult/get", okr.GetAIResult)
		pmGroup.POST("/aiResult/create", okr.CreateAIResult)
		pmGroup.POST("/aiResult/all", okr.BatchGetAiResults)
		pmGroup.DELETE("/aiResult", middleware.CheckAuth, okr.DeleteAiResult)
		// ai_analysis
		pmGroup.GET("/aiAnalysis/get", okr.GetAiAnalysis)
		pmGroup.POST("/aiAnalysis/create", okr.CreateAiAnalysis)
		pmGroup.GET("/aiAnalysis/history", okr.GetAiAnalysisHistory)
		// OKR
		pmGroup.GET("/task/:id/objectives/admin", middleware.CheckAuth, okr.ListAdminTaskObjective)
		pmGroup.GET("/task/:id/objectives/user", middleware.CheckAuth, okr.ListUserTaskObjective)
		pmGroup.GET("/task/:id/user/:username/objectives", middleware.CheckAuth, okr.ListTaskObjectiveByUsername)

		pmGroup.GET("/task/:id/objectives", middleware.CheckAuth, okr.ListObjectiveByTaskId)

	}
	{
		// 运维接口
		pmGroup := r.Group("/admin")
		// 批量更新答案
		pmGroup.POST("/objective/answers/update", middleware.CheckAuth, okr.UpdateObjectiveAnswers)

	}
	return r
}
