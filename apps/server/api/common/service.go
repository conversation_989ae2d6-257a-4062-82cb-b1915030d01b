package common

import (
	"mis/middleware"

	"github.com/gin-gonic/gin"
)

func <PERSON>(c *gin.Context) {
	c.String(200, "Hello World")
}

func Token(c *gin.Context) {
	accessToken := middleware.GetAccessTokenFromContext(c, "accessToken")
	refreshToken := middleware.GetAccessTokenFromContext(c, "refreshToken")
	if accessToken == nil && refreshToken == nil {
		c.JSON(500, gin.H{
			"error": "fail to get token",
		})
		return
	}
	data := gin.H{}
	if accessToken != nil {
		data["accessToken"] = gin.H{
			"key": accessToken.Key,
			"val": accessToken.Val,
		}
	}
	if refreshToken != nil {
		data["refreshToken"] = gin.H{
			"key": refreshToken.Key,
			"val": refreshToken.Val,
		}
	}
	c.JSON(200, gin.H{
		"data": data,
	})
}

func UserInfo(c *gin.Context) {
	user := middleware.GetUsrInfoFromContext(c)
	if user == nil {
		c.JSON(401, gin.H{
			"error": "fail to get user info",
		})
	}
	c.JSON(200, gin.H{
		"data": user,
	})
}
