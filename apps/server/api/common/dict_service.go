package common

import (
	"mis/api/common/models"
	"mis/middleware"
	"mis/pkg/logging"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// CreateDict 处理创建 Dict 的请求
func CreateDict(c *gin.Context) {
	var dict models.Dict
	user := middleware.GetUsrInfoFromContext(c)
	dict.Creator = user.Username
	dict.Updater = user.Username
	if err := c.ShouldBindJSON(&dict); err != nil {
		c.JSON(400, gin.H{
			"error": "无效的请求数据: " + err.Error(),
		})
		return
	}

	if err := models.CreateDictInDB(&dict); err != nil {
		c.JSON(500, gin.H{
			"error": "创建失败: " + err.Error(),
		})
		return
	}
	c.JSON(200, gin.H{
		"data": dict,
	})
}

// UpdateDict 处理更新 Dict 的请求
func UpdateDict(c *gin.Context) {
	var dict models.Dict
	if err := c.ShouldBindJSON(&dict); err != nil {
		c.JSON(400, gin.H{
			"error": "无效的请求数据: " + err.Error(),
		})
		return
	}
	user := middleware.GetUsrInfoFromContext(c)
	dict.Updater = user.Username
	if err := models.UpdateDictInDB(&dict); err != nil {
		c.JSON(500, gin.H{
			"error": "更新失败: " + err.Error(),
		})
		return
	}
	c.JSON(200, gin.H{
		"data": dict,
	})
}

// DeleteDict 处理删除 Dict 的请求
func DeleteDict(c *gin.Context) {
	idsStr := c.Query("ids")
	strArr := strings.Split(idsStr, "_")

	var ids []int
	for _, str := range strArr {
		id, err := strconv.Atoi(str)
		if err != nil {
			c.JSON(400, gin.H{
				"error": "无效的 ID 格式: " + err.Error(),
			})
			return
		}
		ids = append(ids, id)
	}

	user := middleware.GetUsrInfoFromContext(c)
	logging.Logger.Info("delete dict", zap.Any("ids", ids), zap.String("username", user.Username))
	if err := models.DelDictByIds(ids, user.Username); err != nil {
		c.JSON(500, gin.H{
			"error": "删除失败: " + err.Error(),
		})
		return
	}
	c.JSON(200, gin.H{
		"data": ids,
	})
}

func ListDict(c *gin.Context) {
	// 从查询参数中获取搜索条件
	name := c.Query("name")
	// 构建搜索条件
	params := make(map[string]interface{})
	if name != "" {
		params["name"] = name
	}
	dicts, err := models.GetAllDict(params)
	if err != nil {
		c.JSON(500, gin.H{"error": err.Error()})
		return
	}
	c.JSON(200, gin.H{
		"data":    dicts,
		"message": "查询成功",
	})
}
