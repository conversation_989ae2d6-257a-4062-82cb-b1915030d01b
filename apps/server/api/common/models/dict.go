package models

import (
	"net/url"
	"strings"

	"mis/pkg/db"

	"gorm.io/gorm"
)

type Dict struct {
	ID             int    `json:"id" gorm:"column:id;primaryKey"`
	Name           string `json:"name" gorm:"column:name;type:varchar:not null"`
	Value          int    `json:"value" gorm:"column:value;type:tinyint;not null"`
	Type           string `json:"type" gorm:"column:type;type:varchar;not null"`
	Module         string `json:"module" gorm:"column:module;type:varchar;not null"`
	IsDeleted      int    `json:"is_deleted" gorm:"column:is_deleted;type:tinyint"`
	Creator        string `json:"creator" gorm:"column:creator;type:varchar;not null"`
	Updater        string `json:"updater" gorm:"column:updater;type:varchar;not null"`
	CreateDatetime string `json:"gmt_create" gorm:"column:gmt_create;type:datetime;default:null"`
	UpdateDatetime string `json:"gmt_modified" gorm:"column:gmt_modified;type:datetime;default:null"`
}

func (Dict) TableName() string {
	return "fe_mis_dict"
}

// CreateDictInDB 创建一个新的 Dict 记录
func CreateDictInDB(dict *Dict) error {
	return db.DB.Create(dict).Error
}

// DelDictByIds 根据 ID 列表删除 Dict 记录，这里采用软删除，将 is_deleted 字段置为 1
func DelDictByIds(ids []int, username string) error {
	result := db.DB.Model(&Dict{}).Where("id IN (?)", ids).Update("is_deleted", 1).Update("updater", username)
	return result.Error
}

// UpdateDictInDB 更新一个 Dict 记录
func UpdateDictInDB(dict *Dict) error {
	tx := db.DB.Begin()
	if tx.Error != nil {
		return tx.Error
	}
	updateData := map[string]interface{}{
		"name":       dict.Name,
		"value":      dict.Value,
		"type":       dict.Type,
		"module":     dict.Module,
		"is_deleted": dict.IsDeleted,
		"updater":    dict.Updater,
	}
	result := tx.Model(dict).Where("id = ?", dict.ID).Updates(updateData)
	if result.Error != nil {
		tx.Rollback()
		return result.Error
	}
	return tx.Commit().Error
}

func GetAllDict(params map[string]interface{}) ([]*Dict, error) {
	query := db.DB.Where("is_deleted != 1 OR is_deleted IS NULL")
	for key, value := range params {
		if strValue, ok := value.(string); ok {
			// 解码&转义特殊字符 %
			url.QueryUnescape(strValue)
			decodedStr := strings.ReplaceAll(strValue, "%", `\%`)
			// 指定转义字符
			query = query.Where(key+" LIKE ? ", "%"+decodedStr+"%")
		} else {
			// 如果不是字符串类型，还是使用等值查询
			query = query.Where(key+" = ?", value)
		}
	}
	var dicts []*Dict
	err := query.Find(&dicts).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return nil, err
	}
	return dicts, nil
}

func GetDictNameByTypeAndValue(t string, v int) (string, error) {
	var dict Dict
	err := db.DB.Where("type = ? AND value = ? AND (is_deleted != 1 OR is_deleted IS NULL)", t, v).First(&dict).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return "", nil // 未找到记录，返回空字符串和 nil 错误
		}
		return "", err // 其他错误，返回错误信息
	}
	return dict.Name, nil
}
