package main

import (
	"os"
	"time"

	"mis/api"
	"mis/pkg/config"
	"mis/pkg/db"

	"mis/pkg/constant"
	"mis/pkg/env"
	"mis/pkg/logging"

	ginzap "github.com/gin-contrib/zap"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

func main() {
	env.Setup()
	config.Setup()
	constant.Setup()
	if err := logging.Setup(); err != nil {
		panic(err)
	}
	defer logging.Sync()
	db.Setup()
	r := gin.New()
	r.Use(ginzap.GinzapWithConfig(logging.Logger, &ginzap.Config{
		TimeFormat: time.RFC3339,
		UTC:        true,
		SkipPaths:  []string{"/ping"},
	}))
	r.Use(ginzap.RecoveryWithZap(logging.Logger, true))
	api.Init(r)
	port := os.Getenv("PORT")
	if port == "" {
		port = "8000" // 默认端口
	}

	logging.Logger.Info("Server starting on port " + port + " in " + string(env.ENV))
	if err := r.Run(":" + port); err != nil {
		logging.Logger.Error("Server failed to start", zap.Error(err))
	}
}
