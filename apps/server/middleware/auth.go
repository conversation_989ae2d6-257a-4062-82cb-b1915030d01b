package middleware

import (
	"bytes"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"

	"mis/pkg/constant"
	"mis/pkg/env"

	"github.com/gin-gonic/gin"
)

func getAccessTokenByEnv() (string, string) {
	value1 := ""
	value2 := ""
	switch env.ENV {
	case "LOCAL":
		value1 = "local_zkh_access_token"
		value2 = "local_zkh_refresh_token"
	case "UAT":
		value1 = "uat_zkh_access_token"
		value2 = "uat_zkh_refresh_token"
	case "PRO":
		value1 = "zkh_access_token"
		value2 = "zkh_refresh_token"
	}
	return value1, value2
}

func GetUsrInfoFromContext(c *gin.Context) *UserResponse {
	value, exists := c.Get("currentUser")
	if !exists {
		return nil
	}
	user, ok := value.(*UserResponse)
	if !ok {
		return nil
	}
	user.Username = "jing.jin"
	return user
}

func GetAccessTokenFromContext(c *gin.Context, key string) *Token {
	value, exists := c.Get(key)
	if !exists {
		return nil
	}
	token, ok := value.(*Token)
	if !ok {
		return nil
	}
	return token
}

type TokenResponse struct {
	AccessToken  string `json:"access_token"`
	TokenType    string `json:"token_type"`
	ExpiresIn    int    `json:"expires_in"`
	RefreshToken string `json:"refresh_token"`
	Scope        string `json:"scope"`
}

func queryAccessToken(refreshToken string) (*TokenResponse, error) {
	authURL := constant.Hosts.AuthHost + "/server/oauth/token"
	buffer := bytes.NewBufferString(constant.ClientID + ":" + constant.ClientSecret)
	authorization := base64.StdEncoding.EncodeToString(buffer.Bytes())
	authorization = "Basic " + authorization

	// 设置请求的参数（以表单形式为例）
	data := strings.NewReader("grant_type=refresh_token&refresh_token=" + refreshToken)
	// 创建请求
	req, err := http.NewRequest("POST", authURL, data)
	if err != nil {
		fmt.Println("创建请求失败:", err)
		return nil, err
	}

	// 设置请求头
	req.Header.Add("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Add("Authorization", authorization)

	// 创建 HTTP 客户端
	client := &http.Client{}

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		fmt.Println("发送请求失败:", err)
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("请求失败，状态码: %d", resp.StatusCode)
	}

	// 读取响应内容
	var tokenResp TokenResponse
	decoder := json.NewDecoder(resp.Body)
	if err := decoder.Decode(&tokenResp); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}
	return &tokenResp, nil
}

type UserResponse struct {
	Nickname string `json:"nickname"`
	Username string `json:"username"`
}

type Token struct {
	Key string `json:"key"`
	Val string `json:"val"`
}

func GetUserInfo(authToken string) (*UserResponse, error) {
	url := constant.Hosts.SecurityHost + "/accounts/username"
	// 创建请求
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		fmt.Println("创建请求失败:", err)
		return nil, err
	}

	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Authorization", "bearer "+authToken)

	// 创建 HTTP 客户端
	client := &http.Client{}

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		fmt.Println("发送请求失败:", err)
		return nil, err
	}
	defer resp.Body.Close()

	// 读取响应内容
	var userResp UserResponse
	decoder := json.NewDecoder(resp.Body)
	if err := decoder.Decode(&userResp); err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}
	return &userResp, nil
}

func handleUserInfoByToken(c *gin.Context, accessToken string) *UserResponse {
	user, errUser := GetUserInfo(accessToken)
	if errUser == nil && user != nil {
		c.Set("currentUser", user)
		c.Next()
		return user
	}
	return nil
}

func CheckAuth(c *gin.Context) {
	accessTokenStr, refreshTokenStr := getAccessTokenByEnv()
	accessToken, err1 := c.Cookie(accessTokenStr)
	if err1 == nil && accessToken != "" {
		accessTokenObj := Token{
			Key: accessTokenStr,
			Val: accessToken,
		}
		c.Set("accessToken", &accessTokenObj)
		userInfo := handleUserInfoByToken(c, accessToken)
		if userInfo != nil {
			return
		}
	}
	refreshToken, err2 := c.Cookie(refreshTokenStr)
	if err2 == nil && refreshToken != "" {
		tokenStruct, accessTokenErr := queryAccessToken(refreshToken)
		if accessTokenErr == nil && tokenStruct != nil {
			accessTokenObj := Token{
				Key: accessTokenStr,
				Val: tokenStruct.AccessToken,
			}
			c.Set("accessToken", &accessTokenObj)
			refreshTokenObj := Token{
				Key: refreshTokenStr,
				Val: refreshToken,
			}
			c.Set("refreshToken", &refreshTokenObj)
			userInfo := handleUserInfoByToken(c, tokenStruct.AccessToken)
			if userInfo != nil {
				return
			}
		}
	}
	c.JSON(http.StatusUnauthorized, gin.H{"error": "No Authorization"})
	c.AbortWithStatus(http.StatusUnauthorized)
}
