root = "."
tmp_dir = "tmp"

[build]
cmd = "go build -o ./tmp/main ./"
bin = "./tmp/main"
include_ext = [
  "go",
  "tpl",
  "tmpl",
  "html"
]
exclude_dir = [
  "assets",
  "tmp",
  "vendor"
]
delay = 1000
kill_delay = "0.5s"
log = "build-errors.log"
send_interrupt = false
stop_on_error = true

[log]
time = false

[color]
main = "magenta"
watcher = "cyan"
build = "yellow"
runner = "green"

[misc]
clean_on_exit = true
