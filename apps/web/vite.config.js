import process from 'node:process'
import { fileURLToPath, URL } from 'node:url'
import { cssRules, unoCustom } from '@mis/shared/src/uno'
import vue from '@vitejs/plugin-vue'
import UnoCSS from 'unocss/vite'
import { defineConfig } from 'vite'

const isProd = process.env.NODE_ENV === 'production'

const targetUrl = 'https://boss-uat.zkh360.com'
// const targetUrl = 'http://localhost:3000'

export default defineConfig({
  base: isProd ? 'https://files.zkh360.com/assets/mis/okr-web' : '/mis/',
  plugins: [vue(), UnoCSS({
    shortcuts: {
      badge: 'w-8px h-8px inline-block border-rd-50%',
    },
    rules: unoCustom(cssRules),
  })],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
    },
    extensions: ['.js', '.ts', '.vue', '.json', '.scss', '.css'],
  },
  server: {
    allowedHosts: ['local.zkh360.com'],
    host: '0.0.0.0', // 添加这个配置
    proxy: {
      '/api-mis/': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        rewrite: path => path.replace(/^\/api-mis/, ''),
      },
      '/security-api': {
        target: targetUrl,
        changeOrigin: true,
        secure: false,
      },
      '/internal-api': {
        target: targetUrl,
        changeOrigin: true,
        secure: false,
      },
      '/api-agent': {
        target: targetUrl,
        changeOrigin: true,
        secure: false,
      },
      '/api-fe-server': {
        target: targetUrl,
        changeOrigin: true,
        secure: false,
      },
      '/api-oss': {
        target: targetUrl,
        changeOrigin: true,
        secure: false,
      },
    },
  },
})
