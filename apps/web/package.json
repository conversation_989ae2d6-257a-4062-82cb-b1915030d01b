{"name": "@mis/web", "type": "module", "version": "0.0.0", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@mis/shared": "workspace:*", "ali-oss": "^6.23.0", "axios": "^1.9.0", "dayjs": "^1.11.13", "echarts": "^5.6.0", "element-plus": "^2.9.10", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "nzh": "^1.0.14", "unocss": "^66.1.2", "vue": "^3.5.14", "vue-router": "^4.5.1", "vxe-pc-ui": "^4.6.12", "vxe-table": "~4.13.31"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.4", "vite": "^6.3.5"}}