export type UserType = 1 | 2 | 0
export interface User {
  username: string
  nickname: string
  type?: UserType
}

export interface AiResult {
  pastHighlights?: string
  nextProriorities?: string
  risk?: string
  suggestion?: string
  question?: string
  objective_id?: number
  id?: number
}

export interface Record {
  id: number
  value: string
  date: string
  type: number
  is_deleted: number
  objective_id: number
}

export interface Answer {
  id: number
  content: string
  objective_id: number
  question_id: number
  creator: string
  updater: string
  nickname: string
  files?: string
}

export interface Question {
  id: number
  question: string
  is_deleted: number
  objective_id: number
  task_id: number
  type: number
  answers?: Answer[]
}

export interface Objective {
  id: number
  name: string
  tier1: string
  tier2: string
  unit?: number
  records?: Record[]
  unit_name?: string
  questions?: Question[]
  action: string
  year: number
  users?: User[]
  aiResults?: AiResult
}

export interface Task {
  id: number
  year: number
  creator: string
  ai_results?: AiResult[]
}
