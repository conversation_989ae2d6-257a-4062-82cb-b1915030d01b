<script lang="ts" setup>
import * as echarts from 'echarts'
import { onMounted, onUnmounted, ref, watch } from 'vue'

const props = defineProps({
  chartId: {
    type: String,
    required: true,
  },
  options: {
    type: Object,
    required: true,
  },
  style: {
    type: String,
    default: () => 'width: 100%; height: 300px',
  },
})

const chartInstance = ref<echarts.ECharts | null>(null)

// 初始化图表
function initChart() {
  const chartDom = document.getElementById(props.chartId)
  if (!chartDom)
    return

  // 如果已经有实例，先销毁
  if (chartInstance.value) {
    chartInstance.value.dispose()
  }

  chartInstance.value = echarts.init(chartDom)
  chartInstance.value.setOption(props.options)
}

// 监听窗口大小变化，调整图表大小
function handleResize() {
  if (chartInstance.value) {
    chartInstance.value.resize()
  }
}

// 监听 options 变化，更新图表
watch(
  () => props.options,
  (newOptions) => {
    if (chartInstance.value) {
      chartInstance.value.setOption(newOptions)
    }
  },
  { deep: true },
)

onMounted(() => {
  initChart()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  if (chartInstance.value) {
    chartInstance.value.dispose()
    chartInstance.value = null
  }
  window.removeEventListener('resize', handleResize)
})
</script>

<template>
  <div
    :id="chartId"
    :style="style"
    class="bg-#f9f9f9 rounded-5px"
  />
</template>
