<script setup lang="ts">
import type { PropType } from 'vue'
import type { StaffItem } from '@/components/staff-select.vue'
import type { Objective } from '@/types'
import { reactive, ref, watch } from 'vue'
import staffSelect from '@/components/staff-select.vue'
import unitSelect from '@/components/unit-select.vue'

// import useDict from "../hooks/useDict";
// import { urlConfigDict } from "../constants";
export interface FormField {
  tier1: string
  tier2: string
  id: number
  name: string
  unit: number
  action: string
  year: Date
  gmt_create?: string
  gmt_modified?: string
  unitNames?: string[]
  admin?: StaffItem
}
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  actionType: {
    type: String,
    default: 'add',
  },
  detail: {
    type: Object as PropType<Objective>,
    default: () => ({}),
  },
})

const emit = defineEmits(['update:modelValue', 'confirm'])

function formatYear(val: undefined | number) {
  if (!val)
    return new Date()
  if (typeof val === 'number') {
    return new Date(`${val}-12-30`)
  }
  return new Date(val)
}

// const { getPoolList } = useDict(urlConfigDict)
const rules = reactive({
  tier1: [{ required: true, message: '请输入Tier1', trigger: 'blur' }],
  tier2: [{ required: true, message: '请输入Tier2', trigger: 'blur' }],
  name: [{ required: true, message: '请输入OKR', trigger: 'blur' }],
  unit: [{ required: true, message: '请输入单位', trigger: 'blur' }],
  year: [{ required: true, message: '请选择财年', trigger: 'blur' }],
  action: [{ required: true, message: '请输入关键动作', trigger: 'blur' }],
  admin: [{ required: true, message: '请选择管理员', trigger: 'blur' }],
})
const formRef = ref<any>(null)
const formField = reactive<FormField>({
  tier1: props.detail.tier1 || '',
  tier2: props.detail.tier2 || '',
  id: props.detail.id,
  name: props.detail.name || '',
  unit: props.detail.unit,
  action: props.detail.action || '',
  year: formatYear(props.detail.year),
  unitNames: [],
})
const show = ref(props.modelValue)
function handleConfirm() {
  formRef.value
    .validate()
    .then(() => {
      emit('confirm', formField)
      emit('update:modelValue', false)
    })
    .catch((err: any) => {
      console.error(err)
    })
}
// const unitOptions = ref<any[]>([])
watch(
  () => props.modelValue,
  (val) => {
    show.value = val
    formField.tier1 = props.detail.tier1 || ''
    formField.tier2 = props.detail.tier2 || ''
    formField.id = props.detail.id
    formField.name = props.detail.name || ''
    formField.unit = props.detail.unit
    formField.action = props.detail.action || ''
    formField.year = formatYear(props.detail.year)
    formField.unitNames = [props.detail.unit_name]
    // formField.gmt_modified = props.detail.gmt_modified;
    // if (val) {
    //  getPoolList({}).then((res) => {
    //     unitOptions.value = res.filter((item: any) => item.type === 'unit')
    //   })
    // }
  },
  {
    immediate: true,
  },
)
</script>

<template>
  <el-dialog
    v-model="show"
    :title="`${actionType === 'add' ? '新增' : '编辑'}OKR`"
    @click.stop
    @close="emit('update:modelValue', false)"
  >
    <div class="px-32px pt-12px pb-2px">
      <el-form ref="formRef" :model="formField" :rules="rules" :label-width="100">
        <el-form-item v-if="actionType === 'edit'" label="ID" prop="id">
          <el-input v-model="formField.id" disabled placeholder="请输入ID" />
        </el-form-item>
        <el-form-item label="Tier1" prop="tier1">
          <el-input v-model="formField.tier1" placeholder="请输入Tier1" maxlength="30" show-word-limit />
        </el-form-item>
        <el-form-item label="Tier2" prop="tier2">
          <el-input v-model="formField.tier2" placeholder="请输入Tier2" maxlength="30" show-word-limit />
        </el-form-item>
        <el-form-item label="OKR名称" prop="name">
          <el-input v-model="formField.name" placeholder="请输入OKR名称" maxlength="30" show-word-limit />
        </el-form-item>
        <el-form-item label="财年" prop="year">
          <el-date-picker
            v-model="formField.year"
            type="year"
            placeholder="请选择财年"
          />
        </el-form-item>
        <el-form-item label="单位" prop="unit">
          <!-- <el-input v-model="formField.unit" placeholder="请输入单位" /> -->
          <unitSelect v-model="formField.unit" placeholder="请选择单位" :qry-names="formField.unitNames" :multiple="false" />
        </el-form-item>
        <el-form-item label="关键动作" prop="action">
          <el-input v-model="formField.action" placeholder="请输入关键动作" type="textarea" :rows="4" maxlength="500" show-word-limit />
        </el-form-item>
        <el-form-item v-if="actionType === 'add'" label="管理员" prop="admin">
          <staffSelect v-model:option="formField.admin" :multiple="false" />
        </el-form-item>
      </el-form>
    </div>
    <div class="flex justify-center mt-24px pb-12px">
      <el-button @click="emit('update:modelValue', false)">
        取消
      </el-button>
      <el-button type="primary" @click="handleConfirm">
        完成
      </el-button>
    </div>
  </el-dialog>
</template>
