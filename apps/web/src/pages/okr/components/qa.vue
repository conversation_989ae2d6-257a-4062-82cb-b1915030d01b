<script lang="ts" setup>
import type { Objective } from '@/types'
import api from '@mis/shared/src/request'
import { onMounted, ref } from 'vue'

const props = defineProps({
  taskId: {
    type: Number,
    default: 0,
  },
})

const data = ref<Objective[]>([])

const loading = ref(true)

function getNickName(username: string) {
  let nickname = username
  data.value.find((item) => {
    return item.users.find((user) => {
      nickname = user.nickname
      return user.username === username
    })
  })
  return nickname
}
onMounted(async () => {
  try {
    if (!props.taskId) {
      return
    }
    const url = `/api-mis/okr/task/${props.taskId}/objectives/admin`
    const res = await api({
      url,
      method: 'GET',
    })
    if (res.data) {
      data.value = res.data
    }
  }
  catch (e) {
    console.error(e)
  }
  finally {
    loading.value = false
  }
})
</script>

<template>
  <div v-loading="loading">
    <el-table :data="data" style="width: 100%" max-height="800">
      <el-table-column prop="name" label="OKR" width="180" />
      <el-table-column prop="name" label="填报情况">
        <template #default="{ row }">
          <div v-for="(question, idx) in row.questions" :key="question.id" class="text-[12px]">
            <div class="text-[#333]">
              问题{{ idx + 1 }}: {{ question.question
              }}<el-tag v-if="question.task_id" class="ml-1" size="small" type="danger">
                AI
              </el-tag>
            </div>
            <div v-for="answer in question.answers" :key="answer.id" class="pl-2 text-[#666]">
              {{ getNickName(answer.creator) }}: {{ answer.content }}
            </div>
            <div v-if="(question.answers || []).length === 0" class="pl-2 text-[#666]">
              暂无回答
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="user" label="用户" width="180">
        <template #default="{ row }">
          <div class="text-[#333]">
            负责人：<a
              v-for="user in row.users
                .filter((item) => item.type === 1)"
              :key="user.id" class="text-[#333] mr-1 decoration-none" target="_blank" :href="`/mis/okr/weekly/${taskId}?username=${user.username}&year=${row.year}`"
            >{{
              user.nickname }}</a>
          </div>
          <div class="text-[#333]">
            管理人：<a
              v-for="user in row.users
                .filter((item) => item.type === 2)"
              :key="user.id" class="text-[#333] mr-1 decoration-none" target="_blank" :href="`/mis/okr/overview/${taskId}?manager=${user.username}&year=${row.year}`"
            >{{
              user.nickname }}</a>
          </div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
