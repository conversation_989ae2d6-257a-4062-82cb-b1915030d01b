<script setup lang="ts">
import { computed, reactive, ref, watch } from 'vue'
import staffSelect from '../../../components/staff-select.vue'
import { labelMap } from '../constants'
import useUpdateCrud from '../hooks/useUpdateCrud'

interface FormField {
  txtVal: string
  userName: string
  nickName: string
  date: string
  option: Record<string, any>
}
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  type: {
    type: String,
    default: '',
  },
  detail: {
    type: Object,
    default: () => {},
  },
  data: {
    type: Array,
    default: () => [],
  },
  action: {
    type: String,
    default: 'add',
  },
})
const emit = defineEmits(['update:modelValue', 'update:data'])
const {
  handleAddRequest,
  handleUpdateRequest,
} = useUpdateCrud(props.type)
const relatedUserNames = ref<string[]>([])
const modelVisible = computed({
  get() {
    return props.modelValue
  },
  set(newVal) {
    emit('update:modelValue', newVal)
  },
})

const txtLabel = computed(() => {
  return labelMap[props.type] || ''
})

const hasTxt = computed(() => {
  return ['record', 'achieve', 'question'].includes(props.type)
})

const hasUser = computed(() => {
  return ['okrOwner', 'okrManager', 'okrAdmin'].includes(props.type)
})

const modelTitle = computed(() => {
  return `${props.action === 'add' ? '新增' : '编辑'}${labelMap[props.type] || ''}`
})

const modelRules = computed(() => {
  const label = labelMap[props.type] || ''
  const rules = {
    record: {
      txtVal: [{ required: true, message: `请输入${label}`, trigger: 'blur' }],
      date: [{ required: true, message: '请选择日期', trigger: 'blur' }],
    },
    achieve: {
      txtVal: [{ required: true, message: `请输入${label}`, trigger: 'blur' }],
      date: [{ required: true, message: '请选择日期', trigger: 'blur' }],
    },
    question: {
      txtVal: [{ required: true, message: `请输入${label}`, trigger: 'blur' }],
    },
    okrAdmin: {
      userName: [{ required: true, message: `请输入${label}`, trigger: 'blur' }],
    },
    okrOwner: {
      userName: [{ required: true, message: `请输入${label}`, trigger: 'blur' }],
    },
    okrManager: {
      userName: [{ required: true, message: `请输入${label}`, trigger: 'blur' }],
    },
  }
  return rules[props.type] || {}
})

const formData = reactive<FormField>({
  txtVal: '',
  userName: '',
  nickName: '',
  date: '',
  option: {},
})
watch([() => props.detail, () => props.type], ([newVal, newType]) => {
  const fetch = {
    record: () => {
      formData.txtVal = newVal.value || ''
      formData.date = newVal.date || ''
    },
    achieve: () => {
      formData.txtVal = newVal.value || ''
      formData.date = newVal.date || ''
    },
    question: () => {
      formData.txtVal = newVal.question || ''
    },
    okrAdmin: () => {
      formData.userName = newVal.username || ''
      formData.nickName = newVal.nickname || ''
      relatedUserNames.value = [formData.nickName]
    },
    okrOwner: () => {
      formData.userName = newVal.username || ''
      formData.nickName = newVal.nickname || ''
      relatedUserNames.value = [formData.nickName]
    },
    okrManager: () => {
      formData.userName = newVal.username || ''
      formData.nickName = newVal.nickname || ''
      relatedUserNames.value = [formData.nickName]
    },
  }
  fetch[newType] && fetch[newType]()
}, { immediate: true })
const formRef = ref<any>(null)

function handleCancel() {
  emit('update:modelValue', false)
}

function getParams() {
  const isEdit = props.action === 'edit'
  const params = {
    objective_id: props.detail.objective_id,
    is_deleted: 0,
  }
  if (isEdit) {
    Object.assign(params, {
      id: props.detail.id,
    })
  }
  const fetch = {
    record: () => {
      return {
        value: formData.txtVal,
        date: formData.date,
        type: 1,
        ...params,
      }
    },
    achieve: () => {
      return {
        value: formData.txtVal,
        date: formData.date,
        type: 2,
        ...params,
      }
    },
    question: () => {
      return {
        question: formData.txtVal,
        ...params,
      }
    },
    okrAdmin: () => {
      return {
        type: 0,
        username: formData.userName,
        nickname: formData.option.nickname,
        ...params,
      }
    },
    okrOwner: () => {
      return {
        type: 1,
        username: formData.userName,
        nickname: formData.option.nickname,
        ...params,
      }
    },
    okrManager: () => {
      return {
        type: 2,
        username: formData.userName,
        nickname: formData.option.nickname,
        ...params,
      }
    },
  }
  return (fetch[props.type] && fetch[props.type]()) || {}
}
function afterSave(data) {
  const fetch = {
    add: () => {
      // 触发自定义事件通知父组件更新数据
      emit('update:data', [...props.data, {
        ...data,
        showVal: `${data.date}：${data.value}`,
      }])
    },
    edit: () => {
      const [rowData = {}] = data
      const params = (['record', 'achieve'].includes(props.type) && { showVal: `${rowData.date}：${rowData.value}` }) || {}
      const updatedData = props.data.map((item) => {
        if (((item as { id: any }).id) === rowData.id) {
          return Object.assign({}, item, {
            ...rowData,
            ...params,
          })
        }
        return item
      })
      // 触发自定义事件通知父组件更新数据
      emit('update:data', updatedData)
    },
  }
  fetch[props.action] && fetch[props.action]()
}

function formValidate() {
  return new Promise((resolve) => {
    formRef.value.validate((valid: boolean) => {
      if (valid) {
        resolve(true)
      }
      else {
        resolve(false)
      }
    })
  })
}
async function handleSaveClick() {
  const resValidate = await formValidate()
  if (!resValidate)
    return
  const apiFetch = {
    add: handleAddRequest,
    edit: handleUpdateRequest,
  }
  const { res = false, data = {} } = await apiFetch[props.action](props.type, { ...getParams() })
  if (res) {
    afterSave(data)
    emit('update:modelValue', false)
  }
}
</script>

<template>
  <el-dialog v-model="modelVisible" :title="modelTitle" width="500">
    <el-form ref="formRef" :model="formData" :rules="modelRules" :label-width="100">
      <el-form-item v-if="['record', 'achieve'].includes(type)" label="时间维度" prop="date">
        <el-date-picker
          v-model="formData.date"
          type="date"
          placeholder="请选择时间"
          value-format="YYYY-MM-DD"
        />
      </el-form-item>
      <el-form-item v-if="hasTxt" :label="txtLabel" prop="txtVal">
        <el-input v-model="formData.txtVal" placeholder="请输入内容，只需输入数字" />
      </el-form-item>
      <el-form-item v-if="hasUser" :label="txtLabel" prop="userName">
        <staffSelect
          v-model="formData.userName"
          :option="formData.option"
          :multiple="false"
          :related-user-names="relatedUserNames"
          @update:option="formData.option = $event"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">
          取消
        </el-button>
        <el-button type="primary" @click="handleSaveClick">
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped>
::v-deep .el-date-editor.el-input,
::v-deep .el-date-editor.el-input__wrapper {
  width: 100%;
}
</style>
