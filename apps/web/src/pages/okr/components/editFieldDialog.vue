<script setup lang="ts">
import type { okrFields } from '../constants'
import { ElMessageBox } from 'element-plus'
import { computed, watch } from 'vue'
import ZkhTable from '../../../components/zkh-table.vue'
import { labelMap } from '../constants'
import useUpdateCrud from '../hooks/useUpdateCrud'
import addFieldDialog from './addFieldDialog.vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  type: {
    type: String,
    default: '',
  },
  detail: {
    type: Object,
    default: () => {},
  },
})
const emit = defineEmits(['update:modelValue'])
const {
  gridProps,
  gridEvents,
  initColumns,
  handleDeleteRequest,
  addVisible,
  addDetailInfo,
  actionType,
  executeTool,
} = useUpdateCrud(props.type)
const modelVisible = computed({
  get() {
    return props.modelValue
  },
  set(newVal) {
    emit('update:modelValue', newVal)
  },
})

watch([() => props.type, () => props.detail], ([newType, newDetail]) => {
  const fetch = {
    record: () => {
      gridProps.data = (newDetail.records || []).map(item => ({
        ...item,
        showVal: `${item.date}：${item.value}`,
      }))
      addDetailInfo.value.id = newDetail.id || ''
    },
    achieve: () => {
      gridProps.data = (newDetail.achieves || []).map(item => ({
        ...item,
        showVal: `${item.date}：${item.value}`,
      }))
      addDetailInfo.value.id = newDetail.id || ''
    },
    question: () => {
      gridProps.data = newDetail.questions || []
      addDetailInfo.value.id = newDetail.id || ''
    },
    okrAdmin: () => {
      gridProps.data = newDetail.users.filter(i => i.type === 0) || []
      addDetailInfo.value.id = newDetail.id || ''
    },
    okrOwner: () => {
      gridProps.data = newDetail.users.filter(i => i.type === 1) || []
      addDetailInfo.value.id = newDetail.id || ''
    },
    okrManager: () => {
      gridProps.data = newDetail.users.filter(i => i.type === 2) || []
      addDetailInfo.value.id = newDetail.id || ''
    },
  }
  newType && newDetail && fetch[newType] && fetch[newType]()
}, { immediate: true })
watch(() => props.type, (newVal) => {
  gridProps.columns = initColumns(newVal)
})

watch(addVisible, (newVal) => {
  if (newVal && actionType.value === 'add') {
    Object.assign(addDetailInfo.value, {
      objective_id: props.detail.id,
    })
  }
})
function handleCancel() {
  emit('update:modelValue', false)
}
function handleDeleteClick(row) {
  const ids = row.id
  ElMessageBox.confirm(`确定删除吗？`, '提示', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    const res = handleDeleteRequest(props.type, ids)
    if (res)
      gridProps.data = gridProps.data.filter(item => item.id !== ids)
  }).catch(() => {})
}
function handleEdit(row: okrFields) {
  actionType.value = 'edit'
  Object.assign(addDetailInfo.value, row)
  addVisible.value = true
}
</script>

<template>
  <div>
    <el-dialog v-model="modelVisible" :title="`编辑${labelMap[props.type] || ''}`" width="650">
      <div class="mb-10px text-right">
        <el-button plain size="small" type="primary" @click="executeTool('add')">
          创建
        </el-button>
      </div>
      <ZkhTable
        :grid-config="gridProps"
        :grid-events="gridEvents"
      >
        <template #operate="{ row }">
          <el-button plain size="small" type="primary" @click="handleEdit(row)">
            编辑
          </el-button>
          <el-button plain size="small" type="danger" @click="handleDeleteClick(row)">
            删除
          </el-button>
        </template>
      </ZkhTable>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCancel">
            取消
          </el-button>
          <el-button type="primary" @click="handleCancel">
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
    <addFieldDialog v-if="addVisible" v-model="addVisible" :type="type" :action="actionType" :detail="addDetailInfo" :data="gridProps.data" @update:data="gridProps.data = $event" />
  </div>
</template>

<style lang="less" scoped>

</style>
