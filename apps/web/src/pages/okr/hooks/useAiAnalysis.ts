import api from '@mis/shared/src/request'

export function useAiAnalysis() {
  const queryAiAnalysis = async (taskId: number, username: string) => {
    try {
      const res = await api({
        url: '/api-mis/okr/aiAnalysis/get',
        method: 'get',
        params: { taskId, username },
      })
      if (res && res.data && res.data.is_deleted === 0) {
        return res.data
      }
      return null
    }
    catch (error) {
      console.error('查询AI分析时出错:', error)
      return null
    }
  }

  async function saveAiAnalysis(data: {
    task_id: number
    next_priorities: string
    past_highlights: string
    username: string
  }) {
    try {
      const res = await api({
        url: '/api-mis/okr/aiAnalysis/create',
        method: 'post',
        data,
      })

      if (res?.data) {
        return res.data
      }
      else {
        console.error('保存AI分析内容失败：没有返回数据')
        return null
      }
    }
    catch (error) {
      console.error('保存AI分析内容时出错:', error)
      throw error
    }
  }

  return {
    queryAiAnalysis,
    saveAiAnalysis,
  }
}
