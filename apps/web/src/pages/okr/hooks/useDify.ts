import type { AiResult, Objective } from '@/types'
import dayjs from 'dayjs'
import { ref } from 'vue'

export interface OkrWeekInfo {
  OKR名称: string
  目标值: string
  单位: string
  固定问题: string
  关键动作: string
  OKR负责人: string
  OKR管理者: string
  问答内容: string
}

export interface OkrHistoryInfo {
  其他信息: string
}

export interface OkrQueryInfo {
  本周信息: OkrWeekInfo
  历史信息: OkrHistoryInfo
}

export interface StreamRequestOptions {
  onClose?: (accumulatedText: string, aiResult: AiResult) => Promise<void> | void
}

export function useDify() {
  const error = ref<Error | null>(null)

  // 提取风险和建议的函数
  const extractRiskAndSuggestion = (text: string) => {
    const result = {
      risk: '',
      suggestion: '',
      question: '',
    }
    if (typeof text !== 'string')
      return result

    // 风险预警
    const riskMatch = text.match(/风险预警：([\s\S]*?)(?=(\n\n)?改善建议：|$)/)
    if (riskMatch && riskMatch[1]) {
      result.risk = riskMatch[1].trim()
    }

    // 改善建议
    const suggestionMatch = text.match(/改善建议：([\s\S]*?)(?=(\n\n)?提问引导：|$)/)
    if (suggestionMatch && suggestionMatch[1]) {
      result.suggestion = suggestionMatch[1].trim()
    }
    // 提问引导
    const questionMatch = text.match(/提问引导：([\s\S]*?)(?=(\n\n)|$)/)
    if (questionMatch && questionMatch[1]) {
      result.question = questionMatch[1].trim()
    }
    return result
  }

  const getRecentItems = (items: any[], dateKey: string = 'gmt_modified', months: number = 3) => {
    if (!Array.isArray(items))
      return []
    const now = dayjs()
    return items.filter((item) => {
      const date = item[dateKey]
      return date && now.diff(dayjs(date), 'month') <= months
    })
  }
  const formatOkrInfo = (item: Objective) => {
    const recentQuestions = getRecentItems(item?.questions || [])
    const answers = recentQuestions?.filter(q => q.answers)?.flatMap(q => q.answers)
    const recentAnswers = getRecentItems(answers || [])
    return {
      本周信息: {
        OKR名称: item?.name || '',
        目标值: item?.records?.map(r => r.value).join(', ') || '',
        单位: item?.unit_name || '',
        固定问题: recentQuestions?.map(q => q.question).join(', ') || '',
        关键动作: item?.action || '',
        OKR负责人: item?.users?.filter(u => u.type === 1).map(u => u.nickname).join(', ') || '',
        OKR管理者: item?.users?.filter(u => u.type === 2).map(u => u.nickname).join(', ') || '',
        问答内容: recentAnswers?.map(a => a.content)?.join(', ') || '',
      },
      历史信息: {
        其他信息: '这里放图片ocr的内容',
      },
    }
  }
  const handleDifyRequest = async (
    appKey: string,
    queryInfo: OkrQueryInfo | OkrQueryInfo[],
    username: string,
    options?: StreamRequestOptions,
  ): Promise<{ text: string, aiResult: AiResult }> => {
    error.value = null

    try {
      const requestParams = {
        inputs: {
          info: JSON.stringify(queryInfo),
        },
        response_mode: 'blocking',
        user: username,
      }

      const response = await fetch('/api-agent/v1/workflows/run', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${appKey}`,
        },
        body: JSON.stringify(requestParams),
      })

      if (!response.ok) {
        throw new Error(`请求失败: ${response.status}`)
      }

      const data = await response.json()

      // 处理返回的数据
      const text = data.data?.outputs?.text || ''
      const extracted = extractRiskAndSuggestion(text)

      const aiResult: AiResult = {
        risk: extracted.risk || '无风险分析',
        suggestion: extracted.suggestion || '无改善建议',
        question: extracted.question || '',
      }

      // 调用 onClose 回调
      if (options?.onClose) {
        await options.onClose(text, aiResult)
      }

      return { text, aiResult }
    }
    catch (err) {
      error.value = err as Error
      console.error('AI分析请求出错:', err)
      throw err
    }
  }

  return {
    error,
    formatOkrInfo,
    handleDifyRequest,
    extractRiskAndSuggestion,
  }
}
