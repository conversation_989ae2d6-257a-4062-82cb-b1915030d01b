import type { IpageInfo } from '@mis/shared'
import type { VxeGridListeners, VxeGridProps } from 'vxe-table'
import type { User } from '@/types'
import { sendWechatMsg, uniqueArrayByProperty } from '@mis/shared'
import api from '@mis/shared/src/request'
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
import { reactive, ref } from 'vue'
import { columnFieldsTask, urlConfigTask } from '../constants'
import { handleAIRequestRistAndSuggestion, saveAiResult } from '../utils'

export default function useTask() {
  const formRef = ref()
  const defaultPageInfo = {
    pageNum: 1,
    pageSize: 10,
  } as const
  const pageInfo = reactive<IpageInfo>({
    ...defaultPageInfo,
    total: 0,
  })
  const gridProps = reactive<VxeGridProps>({
    border: true,
    loading: false,
    columns: columnFieldsTask,
    maxHeight: 640,
    data: [],
    // toolbarConfig: {
    //   tools: [{ name: '创建', status: 'primary', code: 'add' }],
    // },
    columnConfig: {
      resizable: true,
    },
    showOverflow: false,
  })
  const addVisible = ref<boolean>(false)
  const getParams = (form: Record<string, any>) => {
    const formFields = {
      ...form,
    }
    return {
      ...formFields,
      pageNum: pageInfo.pageNum,
      pageSize: pageInfo.pageSize,
    }
  }
  // 列表查询
  const getPoolList = async (form: Record<string, any>) => {
    const data = getParams(form)
    try {
      gridProps.loading = true
      const res = await api({
        url: urlConfigTask.list,
        method: 'get',
        params: data,
      })
      if (res?.data) {
        pageInfo.total = res.total || res.data.length
        gridProps.data = (res.data || []).map((item: any) => {
          const questions = item?.questions?.filter((question: any) => question?.objective?.year === item.year && question.question)
          return {
            ...item,
            questions,
          }
        })
      }
    }
    catch (err) {
      console.error(err)
    }
    finally {
      gridProps.loading = false
    }
  }

  const form = ref<any>({})
  const rowInfo = ref<any>({})
  const actionType = ref<string>('add')
  const taskLoading = ref<boolean>(false)
  // 新增
  const handleAddTask = async (year: string, username: string) => {
    const url = urlConfigTask.add
    taskLoading.value = true
    try {
      const res = await api({ url, method: 'post', data: { year: Number(year) } })
      if (res.data) {
        // 通过ai计算风险及建议
        const { data: objectives } = await api({
          url: `/api-mis/okr/year/${year}/objectives`,
          method: 'get',
        })
        for (const objective of objectives) {
          const { risk, suggestion, question: newQuestion } = await handleAIRequestRistAndSuggestion(objective, username)
          if (!risk || !suggestion || !newQuestion) {
            ElMessage({
              message: `${objective.name}，AI生成内容失败！`,
              type: 'warning',
            })
            continue
          }
          const [res3, res4] = await Promise.all([saveAiResult({
            objective_id: objective.id,
            task_id: res.data,
            risk: risk || '',
            suggestion: suggestion || '',
          }), api({
            url: `/api-mis/okr/task/${res.data}/question`,
            method: 'post',
            data: {
              objective_id: objective.id,
              question: newQuestion,
            },
          })])
          if (res3.data && res4.data) {
            ElMessage({
              message: `${objective.name}，AI生成内容成功！`,
              type: 'success',
            })
          }
        }
        let { data: users } = await api({
          url: '/api-mis/okr/users',
          method: 'get',
          params: {
            year,
          },
        })
        const link = `${location.origin}/mis/okr/weekly/${res.data}?year=${year}`
        users = users.filter((item: User) => item.type === 1)
        users = uniqueArrayByProperty(users, 'username')
        const _url = `https://boss-h5.zkh360.com/open?url=${encodeURIComponent(link)}`
        const content = `诸位东主大人台鉴：<br />昔张岱雪夜拏舟，今诸君键盘生风。烦移步此<a href="${_url}">查看</a>，效陶元亮种柳五株：一株填战果，二株理经纬，三株问阻滞，四株策来日，五株缀闲笔三分。日日苦短，周报不等人。若延至明日，砚中恐爬出老板急脚递，怒题《满江红·何不交》！<br /><br />——赛博书童震坤行小O顿首`
        for (const user of users) {
          sendWechatMsg(user.username, content)
        }
        ElNotification({
          title: '消息通知',
          message: '任务发起成功',
          type: 'success',
        })
      }
    }
    catch (error) {
      console.error(error)
    }
    finally {
      taskLoading.value = false
    }
  }
  // 发送周报
  const handleSendReport = async (taskId: number, year: number) => {
    const link = `${location.origin}/mis/okr/overview/${taskId}?year=${year}`
    let { data: users } = await api({
      url: '/api-mis/okr/users',
      method: 'get',
      params: {
        year,
      },
    })
    users = users.filter((item: User) => item.type === 2)
    users = uniqueArrayByProperty(users, 'username')
    const _url = `https://boss-h5.zkh360.com/open?url=${encodeURIComponent(link)}`
    const content = `【周报观沧海】<br />诸君墨宝已入匣，AI夜烹数据茶。<br />速击此<a href="${_url}">查看</a>，且观赫赫战功淬鳞甲。<br /><br />——电子师爷震坤行小O叩禀`
    for (const user of users) {
      sendWechatMsg(user.username, content)
    }
  }
  // 编辑
  const handleEdit = async (form: Record<string, any>, type: string) => {
    rowInfo.value = form
    actionType.value = type
    addVisible.value = true
  }
  const handleConfirm = async (form: Record<string, any>) => {
    const url
      = actionType.value === 'add' ? urlConfigTask.add : urlConfigTask.update
    const method = actionType.value === 'add' ? 'post' : 'put'
    form.value = Number(form.value)
    const res = await api({ url, method, data: form })
    if (res) {
      ElMessage({
        type: 'success',
        message: `操作成功！`,
      })
      getPoolList(formRef.value?.formFieldData)
      addVisible.value = false
    }
  }
  // 右侧回调事件
  const executeTool = (code: string) => {
    switch (code) {
      case 'add':
        // to-do 是否还需要调用
        // handleAddTask()
        break
      default:
        break
    }
  }
  const gridEvents: VxeGridListeners = {
    toolbarToolClick({ code }) {
      executeTool(code)
    },
  }
  // 删除
  const handleDeleteRequest = async (ids: number[]) => {
    const url = urlConfigTask.delete
    const res = await api({
      url,
      method: 'delete',
      params: { ids: ids.join('_') },
    })
    if (res) {
      ElMessage({
        type: 'success',
        message: `删除成功！`,
      })
      getPoolList(formRef.value?.formFieldData)
      return true
    }
    else {
      return false
    }
  }
  const handleDelete = (ids: number[]) => {
    return new Promise<boolean>((resolve) => {
      ElMessageBox.confirm(`确定删除吗？`, '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          resolve(await handleDeleteRequest(ids))
        })
        .catch(() => {
          resolve(false)
        })
    })
  }
  return {
    gridProps,
    formRef,
    pageInfo,
    defaultPageInfo,
    getPoolList,
    gridEvents,
    form,
    handleDelete,
    addVisible,
    handleAddTask,
    handleEdit,
    handleConfirm,
    rowInfo,
    actionType,
    taskLoading,
    sendWechatMsg,
    handleSendReport,
  }
}
