import type { Objective } from '@/types'

export function useChartOptions(objective: Objective) {
  try {
    if (!objective.records || !Array.isArray(objective.records) || objective.records.length === 0) {
      return {}
    }

    const sortedRecords = [...objective.records].sort((a, b) => {
      const dateA = new Date(a.date).getTime()
      const dateB = new Date(b.date).getTime()
      return dateA - dateB
    })

    const dateList = sortedRecords.filter(r => r.is_deleted !== 1).map(it => it.date)
    const targetRecords = sortedRecords.filter(r => r.type === 1 && r.is_deleted !== 1)
    const actualRecords = sortedRecords.filter(r => r.type === 2 && r.is_deleted !== 1)

    const targetSeriesData = dateList.map((date) => {
      const target = targetRecords.find(r => r.date === date)
      return target ? Number(target.value) : null
    })

    const actualSeriesData = dateList.map((date) => {
      const actual = actualRecords.find(r => r.date === date)
      return actual ? Number(actual.value) : null
    })

    // 返回图表配置
    return {
      title: {
        text: objective.name,
      },
      grid: {
        right: '10%',
        left: '15%',
      },
      tooltip: {
        trigger: 'item',
        axisPointer: {
          type: 'cross',
          label: {
            backgroundColor: '#6a7985',
          },
        },
      },
      legend: {
        data: ['目标值', '实际值'],
        top: 20,
        right: 20,
        textStyle: {
          color: '#333',
          fontSize: 12,
        },
      },
      xAxis: {
        type: 'category',
        data: dateList,
        boundaryGap: false,
      },
      yAxis: {
        type: 'value',
        scale: true,
      },
      series: [
        {
          name: '目标值',
          type: 'line',
          data: targetSeriesData,
        },
        {
          name: '实际值',
          type: 'line',
          data: actualSeriesData,
        },
      ],
    }
  }
  catch (error) {
    console.error('生成图表配置出错:', error)
    return {}
  }
}
