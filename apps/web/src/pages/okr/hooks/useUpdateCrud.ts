import type { VxeGridListeners, VxeGridProps } from 'vxe-table'
import type {
  okrFields,
} from '../constants'
import api from '@mis/shared/src/request'
import { ElMessage } from 'element-plus'
import { reactive, ref } from 'vue'
import {
  achieveColumns,
  okrAdminColumns,
  okrManagerColumns,
  okrOwnerColumns,
  questionColumns,
  recordColumns,
  updateAPIFetch,
} from '../constants'

export default function useUpdateCrud(type: string) {
  const columnMap = {
    record: recordColumns,
    achieve: achieveColumns,
    question: questionColumns,
    okrAdmin: okrAdminColumns,
    okrOwner: okrOwnerColumns,
    okrManager: okrManagerColumns,
  }
  const addVisible = ref(false)
  const addDetailInfo = ref<okrFields>({})
  const actionType = ref('add')
  const gridProps = reactive<VxeGridProps>({
    border: true,
    loading: false,
    columns: columnMap[type],
    maxHeight: 640,
    data: [],
    columnConfig: {
      resizable: true,
    },
    showOverflow: true,
  })

  const handleUpateField = () => {
    addDetailInfo.value = {}
    actionType.value = 'add'
    addVisible.value = true
  }
  const executeTool = (code: string) => {
    switch (code) {
      case 'add':
        handleUpateField()
        break
      default:
        break
    }
  }
  const gridEvents: VxeGridListeners = {
    toolbarToolClick({ code }) {
      executeTool(code)
    },
  }
  const initColumns = (type: string) => {
    return columnMap[type] || []
  }
  const handleDeleteRequest = async (type: string, ids: string) => {
    const apiFetch = {
      record: updateAPIFetch.delRecords,
      achieve: updateAPIFetch.delRecords,
      question: updateAPIFetch.delQuestions,
      okrAdmin: updateAPIFetch.delUsers,
      okrOwner: updateAPIFetch.delUsers,
      okrManager: updateAPIFetch.delUsers,
    }
    const url = apiFetch[type]
    const { data = [] } = await api({ url, method: 'delete', params: { ids } })
    if (!data.length) {
      ElMessage({
        type: 'error',
        message: `操作失败！`,
      })
      return false
    }
    return true
  }

  const handleAddRequest = async (type: string, formData: Record<string, any>) => {
    const apiFetch = {
      record: updateAPIFetch.addRecords,
      achieve: updateAPIFetch.addRecords,
      question: updateAPIFetch.addQuestions,
      okrAdmin: updateAPIFetch.addUsers,
      okrOwner: updateAPIFetch.addUsers,
      okrManager: updateAPIFetch.addUsers,
    }
    const url = apiFetch[type]
    const { error = '', data = {} } = await api({ url, method: 'post', data: { ...formData } })
    if (error) {
      ElMessage({
        type: 'error',
        message: `操作失败！`,
      })
      return {
        res: false,
      }
    }
    return {
      res: true,
      data,
    }
  }

  const handleUpdateRequest = async (type: string, formData: Record<string, any>) => {
    const apiFetch = {
      record: updateAPIFetch.updateRecords,
      achieve: updateAPIFetch.updateRecords,
      question: updateAPIFetch.updateQuestions,
      okrAdmin: updateAPIFetch.updateUsers,
      okrOwner: updateAPIFetch.updateUsers,
      okrManager: updateAPIFetch.updateUsers,
    }
    const url = apiFetch[type]
    const { error = '', data = [] } = await api({ url, method: 'put', data: [{ ...formData }] })
    if (error) {
      ElMessage({
        type: 'error',
        message: `操作失败！`,
      })
      return {
        res: false,
      }
    }
    return {
      res: true,
      data,
    }
  }
  return {
    gridProps,
    gridEvents,
    initColumns,
    handleDeleteRequest,
    addVisible,
    addDetailInfo,
    handleAddRequest,
    actionType,
    handleUpdateRequest,
    executeTool,
  }
}
