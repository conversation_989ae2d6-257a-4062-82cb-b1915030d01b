import type { IpageInfo } from '@mis/shared'
import type { VxeGridProps } from 'vxe-table'
import api from '@mis/shared/src/request'
import { ElMessage, ElMessageBox } from 'element-plus'
import { reactive, ref } from 'vue'
import { useRoute } from 'vue-router'
import { columnFieldsJobDetail, urlConfigJobDetail } from '../constants'

export default function useJob() {
  const route = useRoute()
  const jobId = route.query.jobId as string
  const formRef = ref()
  const form = ref<any>({
    jobId,
  })
  const defaultPageInfo = {
    pageNum: 1,
    pageSize: 10,
  } as const
  const pageInfo = reactive<IpageInfo>({
    ...defaultPageInfo,
    total: 0,
  })
  const gridProps = reactive<VxeGridProps>({
    border: true,
    loading: false,
    columns: columnFieldsJobDetail,
    maxHeight: 640,
    data: [],
    // toolbarConfig: {
    //   tools: [{ name: '创建', status: 'primary', code: 'add' }],
    // },
    columnConfig: {
      resizable: true,
    },
    showOverflow: false,
  })
  const addVisible = ref<boolean>(false)
  const getParams = (form: Record<string, any>) => {
    const formFields = {
      ...form,
    }
    return {
      ...formFields,
      pageNum: pageInfo.pageNum,
      pageSize: pageInfo.pageSize,
    }
  }
  // 列表查询
  const getPoolList = async (form: Record<string, any>) => {
    const data = getParams(form)
    try {
      gridProps.loading = true
      const res = await api({
        url: urlConfigJobDetail.list,
        method: 'get',
        params: data,
      })
      if (res?.data) {
        pageInfo.total = res.total || res.data.length
        gridProps.data = res.data || []
      }
    }
    catch (err) {
      console.error(err)
    }
    finally {
      gridProps.loading = false
    }
  }
  // 删除
  const handleDeleteRequest = async (ids: number[]) => {
    const url = urlConfigJobDetail.delete
    const res = await api({
      url,
      method: 'delete',
      params: { ids: ids.join('_') },
    })
    if (res) {
      ElMessage({
        type: 'success',
        message: `删除成功！`,
      })
      getPoolList(form.value)
      return true
    }
    else {
      return false
    }
  }
  const handleDelete = (ids: number[]) => {
    return new Promise<boolean>((resolve) => {
      ElMessageBox.confirm(`确定删除吗？`, '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          resolve(await handleDeleteRequest(ids))
        })
        .catch(() => {
          resolve(false)
        })
    })
  }
  return {
    gridProps,
    formRef,
    pageInfo,
    defaultPageInfo,
    getPoolList,
    form,
    addVisible,
    handleDelete,
  }
}
