<script setup lang="ts">
import type { InjectUser } from '@/hooks/useUser'
import dayjs from 'dayjs'
import { ElMessageBox } from 'element-plus'
import { inject, onMounted, watch } from 'vue'
import ZkhTable from '@/components/zkh-table.vue'
import StaffSelect from '../../components/staff-select.vue'
import AddDialog from './components/add.vue'
import editFieldDialog from './components/editFieldDialog.vue'
import { urlConfig } from './constants'
import useListCrud from './hooks/useCrud'
import useTask from './hooks/useTask'

const user = inject<InjectUser>('user')
const {
  gridProps,
  gridEvents,
  getPoolList,
  pageInfo,
  formRef,
  form,
  addVisible,
  handleAdd,
  handleDelete,
  handleEdit,
  rowInfo,
  actionType,
  handleConfirm,
  editVisible,
} = useListCrud(urlConfig)

watch(editVisible, (newVal) => {
  !newVal && getPoolList({
    ...form.value,
    year: formatYear(form.value.year),
  })
})
const { handleAddTask, taskLoading } = useTask()
function handleQuery() {
  pageInfo.pageNum = 1
  getPoolList({
    ...form.value,
    year: formatYear(form.value.year),
  })
}
function handleReset() {
  form.value = {}
  pageInfo.pageNum = 1
  pageInfo.pageSize = 10
  getPoolList(form.value)
}
function handlePageChange({ pageNum, pageSize }: { pageNum: number, pageSize: number }) {
  pageInfo.pageNum = pageNum
  pageInfo.pageSize = pageSize
  getPoolList({
    ...form.value,
    year: formatYear(form.value.year),
  })
}
function handleStartTask() {
  ElMessageBox.prompt('请输入财年', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputPattern:
      /^(19|20)\d{2}$/,
    inputErrorMessage: '无效格式',
    inputValue: (new Date().getFullYear()).toString(),
  })
    .then(({ value }) => {
      handleAddTask(value, user.value.username)
    })
}
function formatYear(val: undefined | number) {
  if (!val)
    return val
  if (typeof val === 'number') {
    return dayjs(`${val}-12-30`, 'YYYY-MM-DD')
  }
  return dayjs(val).format('YYYY')
}
onMounted(() => {
  getPoolList(form.value)
})
</script>

<template>
  <div class="bg-white p-12px min-h-440px">
    <el-card title="OKR管理列表">
      <template #header>
        <div class="text-16px font-700 mb-12px">
          OKR管理列表
        </div>
        <el-form
          ref="formRef"
          :inline="true"
        >
          <el-form-item label="OKR名称" prop="name">
            <el-input v-model="form.name" placeholder="请输入名称" @keyup.enter="handleQuery" />
          </el-form-item>
          <el-form-item label="负责人" prop="username">
            <StaffSelect
              v-model="form.username"
              :multiple="false"
              placeholder="请输入负责人"
            />
          </el-form-item>
          <el-form-item label="财年" prop="year">
            <el-date-picker
              v-model="form.year"
              type="year"
              placeholder="请选择财年"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">
              查询
            </el-button>
            <el-button @click="handleReset">
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </template>
      <div class="flex justify-between my-2">
        <span class="color-#999">共{{ pageInfo.total }}条数据</span>
        <div>
          <el-button type="primary" @click="handleAdd({})">
            创建
          </el-button>
          <el-button type="warning" :loading="taskLoading" :disabled="pageInfo.total === 0" @click="handleStartTask">
            发起
          </el-button>
        </div>
      </div>
      <ZkhTable
        :page-info="pageInfo"
        :grid-config="gridProps"
        :grid-events="gridEvents"
        @page-change="handlePageChange"
      >
        <template #target="{ row }">
          <div v-for="item in row.records" :key="item.id" class="color-#333 mb-6px">
            <span class="color-#666 mr-6px">{{ item.date }}</span>
            <el-tag type="danger" size="small">
              {{ item.value }}
            </el-tag>
          </div>
        </template>
        <template #achieve="{ row }">
          <div v-for="item in row.achieves" :key="item.id" class="color-#333 mb-6px">
            <span class="color-#666 mr-6px">{{ item.date }}</span>
            <el-tag type="danger" size="small">
              {{ item.value }}
            </el-tag>
          </div>
        </template>
        <template #question="{ row }">
          <div v-for="(item, index) in row.questions.filter(i => i.type !== 1)" :key="item.id" class="color-#333 mb-6px">
            {{ index + 1 }}.{{ item.question }}
          </div>
        </template>
        <template #admin="{ row }">
          <div v-for="item in row.users.filter(i => i.type === 0)" :key="item.id" class="color-#333 mb-6px">
            {{ item.nickname }}
          </div>
        </template>
        <template #owner="{ row }">
          <div v-for="item in row.users.filter(i => i.type === 1)" :key="item.id" class="color-#333 mb-6px">
            {{ item.nickname }}
          </div>
        </template>
        <template #manager="{ row }">
          <div v-for="item in row.users.filter(i => i.type === 2)" :key="item.id" class="color-#333 mb-6px">
            {{ item.nickname }}
          </div>
        </template>
        <template #operate="{ row }">
          <el-row justify="center">
            <el-button class="mb-3px ml-12px" plain type="primary" size="small" @click="handleEdit(row, 'record')">
              目标值
            </el-button>
            <el-button class="mb-3px" plain type="primary" size="small" @click="handleEdit(row, 'achieve')">
              实际值
            </el-button>
          </el-row>
          <el-row justify="center">
            <el-button class="mb-3px" plain type="primary" size="small" @click="handleEdit(row, 'okrAdmin')">
              管理员
            </el-button>
            <el-button class="mb-3px" plain type="primary" size="small" @click="handleEdit(row, 'okrOwner')">
              负责人
            </el-button>
            <el-button class="mb-3px" plain type="primary" size="small" @click="handleEdit(row, 'okrManager')">
              管理者
            </el-button>
          </el-row>
          <el-row justify="center">
            <el-button class="mb-3px" plain type="primary" size="small" @click="handleEdit(row, 'question')">
              固定问题
            </el-button>
          </el-row>
          <el-button class="mb-3px" plain type="primary" size="small" @click="handleEdit(row, 'edit')">
            编辑
          </el-button>
          <el-button class="mb-3px" plain type="danger" size="small" @click="handleDelete([row.id])">
            删除
          </el-button>
        </template>
      </ZkhTable>
    </el-card>
    <AddDialog
      v-if="addVisible"
      v-model="addVisible"
      :detail="rowInfo"
      :action-type="actionType"
      @confirm="handleConfirm"
    />
    <editFieldDialog
      v-if="editVisible"
      v-model="editVisible"
      :detail="rowInfo"
      :type="actionType"
    />
  </div>
</template>
