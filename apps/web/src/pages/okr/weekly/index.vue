<script lang="ts" setup>
import type { InjectUser } from '@/hooks/useUser'
import type { Objective } from '@/types'
import api from '@mis/shared/src/request'
import { computed, inject, onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import { useDify } from '../hooks/useDify'
import { saveAiResult } from '../utils'
import { getSetp1AppKey } from '../utils/appKey'
import ChartComponent from './components/chart.vue'
import ProblemForm from './components/problem-form.vue'

type Mode = 'view' | 'fill'

const { formatOkrInfo, handleDifyRequest } = useDify()
const user = inject<InjectUser>('user')
const route = useRoute()
const taskId = Number(route.params.id)
const currentUsername = route.query.username
const year = Number(route.query.year || new Date().getFullYear())
const okrList = ref<Objective[]>([])

const mode = computed<Mode>(() => {
  if (!currentUsername || user.value?.username === currentUsername) {
    return 'fill'
  }
  return 'view'
})
const username = computed(() => {
  return (currentUsername || user.value?.username || '') as string
})

const nickname = computed(() => {
  if (mode.value === 'fill') {
    return user.value?.nickname || ''
  }
  let nickname = username.value
  okrList.value.find((item) => {
    return item.users.find((objectiveUser) => {
      if (objectiveUser.username === username.value) {
        nickname = objectiveUser.nickname
      }
      return objectiveUser.username === username.value
    })
  })
  return nickname
})
const pageLoading = ref(false)
// 列表查询
async function getList() {
  try {
    if (!username.value) {
      return
    }
    pageLoading.value = true
    const res = await api({
      url: `/api-mis/okr/task/${taskId}/user/${username.value}/objectives`,
      method: 'GET',
    })
    if (res?.data) {
      okrList.value = res.data.filter((item: Objective) => item.year === year)
      pageLoading.value = false
      const promises = okrList.value.map(item => checkAndProcessAiResult(item))
      await Promise.all(promises)
    }
  }
  catch (err) {
    console.error(err)
  }
  finally {
    pageLoading.value = false
  }
}

// 查询数据库中是否有AI结果
async function queryAiResult(objectiveId: number) {
  try {
    const result = await api({
      url: '/api-mis/okr/aiResult/get',
      method: 'get',
      params: { objectiveId, taskId },
    })
    return result
  }
  catch (error) {
    console.error('查询AI结果时出错:', error)
    return null
  }
}

// 处理流式请求的方法
async function handleAIRequest(_item: Objective) {
  try {
    const currentItem = okrList.value.find((item: Objective) => item.id === _item.id)

    // 构建完整的请求参数
    const queryInfo = formatOkrInfo(currentItem)

    currentItem.aiResults = currentItem.aiResults || {
      risk: '正在生成风险分析...',
      suggestion: '正在生成建议...',
    }
    const appKey = getSetp1AppKey()
    const result = handleDifyRequest(appKey, queryInfo, username.value, {
      onClose: async (accumulatedText, aiResult) => {
        // 保存AI结果到数据库
        try {
          if (currentItem && aiResult) {
            currentItem.aiResults.risk = aiResult.risk || '暂无风险分析'
            currentItem.aiResults.suggestion = aiResult.suggestion || '暂无生成建议'
          }
          // 添加动态问题
          if (aiResult?.question) {
            const addQuestionRes = await api({
              url: `/api-mis/okr/task/${taskId}/question`,
              method: 'post',
              data: {
                objective_id: currentItem.id,
                question: aiResult.question,
              },
            })
            if (addQuestionRes?.data?.id) {
              currentItem.questions.push({
                ...addQuestionRes.data,
                question: aiResult.question,
              })
            }
          }
          await saveAiResult({
            objective_id: currentItem.id,
            task_id: taskId,
            risk: aiResult.risk || '',
            suggestion: aiResult.suggestion || '',
          })
        }
        catch (error) {
          console.error('保存AI结果时出错:', error)
        }
      },
    })
    return result
  }
  catch (error) {
    console.error('处理流式请求时出错:', error)
    throw error
  }
}

async function checkAndProcessAiResult(item: Objective) {
  try {
    // 先查询是否已有AI结果
    const aiResult = await queryAiResult(item.id)

    if (aiResult && aiResult.data && aiResult.data.risk && aiResult.data.suggestion) {
      console.log(`已找到OKR ID ${item.id}的AI结果，无需重新请求`)
      const currentItem = okrList.value.find((i: any) => i.id === item.id)
      currentItem.aiResults = aiResult.data
    }
    else {
      console.warn(`未找到OKR ID ${item.id}的AI结果，准备请求AI接口`)
      await handleAIRequest(item)
    }
  }
  catch (error) {
    console.error('检查AI结果时出错:', error)
  }
}

onMounted(() => {
  getList()
})
</script>

<template>
  <div v-loading="pageLoading" class="w-100% bg-#f7f7f7">
    <el-card class="w-1200px mx-auto">
      <div class="flex justify-between">
        <div class="flex items-center">
          <span>
            <span class="font-700">{{ nickname }}</span>
            您好，已为您智能预测OKR风险和建议，请填写您的OKR进展
          </span>
        </div>
        <!-- <div class="flex items-center">
          <el-date-picker
            v-model="dateTime"
            type="date"
            placeholder="请选择时间"
            value-format="YYYY-MM-DD"
          />
          <el-button type="primary" @click="getPoolList">查询</el-button>
        </div> -->
      </div>
    </el-card>
    <template v-if="okrList.length === 0 && !pageLoading">
      <el-empty description="暂无数据,您已经完成全部任务" />
    </template>
    <template v-else>
      <div v-for="item in okrList" :key="item.id" class="report-view">
        <ChartComponent :item="item" />
        <ProblemForm :item="item" :task-id="taskId" :disabled="mode === 'view' || item.questions.some(q => (q.answers || []).length > 0)" :username="username" :mode="mode" />
      </div>
    </template>
  </div>
</template>

<style scoped>
.report-view {
  width: 1200px;
  display: flex;
  justify-content: space-between;
  border-radius: 12px;
  margin: 12px auto;
  background: #fff;
}
</style>
