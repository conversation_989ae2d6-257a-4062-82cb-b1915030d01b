<script lang="ts" setup>
import type { UploadFile } from 'element-plus/lib/components/upload'
import type { InjectUser } from '@/hooks/useUser'
import api from '@mis/shared/src/request'
import { computed, inject, reactive, ref, watch } from 'vue'
import ImagePreview from '@/components/ImagePreview.vue'
import UploadFiles from '@/components/Upload.vue'

const props = defineProps({
  item: {
    type: Object,
    required: true,
  },
  taskId: {
    type: Number,
    required: true,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  username: {
    type: String,
    required: true,
  },
  mode: {
    type: String,
    default: '',
  },
})
const questions = computed(() => props.item.questions.filter(q => q.is_deleted !== 1))
const form = reactive({})
const formRef = ref(null)
const user = inject<InjectUser>('user')
// 动态生成校验规则
const rules = reactive({})
// 存储上传成功的文件列表
const uploadedFilesMap = ref<Record<string, UploadFile[]>>({})
// 监听 questions 变化，重新生成规则
watch(questions, (newQuestions) => {
  // 清空旧规则
  Object.keys(rules).forEach((key) => {
    delete rules[key]
  })
  newQuestions.forEach((it) => {
    rules[`question${it.id}`] = [
      { required: true, message: `请输入${it.question}答案`, trigger: 'change' },
    ]
    // 初始化 form 字段
    const answer = props.mode === 'view' ? it.answers[0] : it.answers?.findLast(a => a.creator === props.username)
    form[`question${it.id}`] = answer?.content || ''
  })
  // 初始化uploadedFilesMap
  newQuestions.forEach((it) => {
    const answer = props.mode === 'view' ? it.answers[0] : it.answers?.findLast(a => a.creator === props.username)
    if (answer?.files) {
      uploadedFilesMap.value[it.id] = answer.files.split(',').map(url => ({
        url,
        name: decodeURIComponent(url.split('/').pop()?.split('?')[0]),
        uid: url,
      }))
    }
  })
}, { immediate: true })
const isSubmit = ref(false)
function handleSubmit() {
  if (!formRef.value)
    return
  formRef.value.validate(async (valid) => {
    if (valid) {
      // 提交表单
      const data = questions.value.map(it => ({
        question_id: it.id,
        objective_id: it.objective_id,
        content: form[`question${it.id}`],
        task_id: props.taskId,
        files: uploadedFilesMap.value[it.id]?.map(file => file.url)?.join(','),
        username: user.value.username || '',
        nickname: user.value.nickname || '',
      }))
      await api({
        url: `/api-mis/okr/objective/answers`,
        method: 'post',
        data: {
          answers: data,
        },
      })
      isSubmit.value = true
    }
    else {
      console.error('表单验证失败')
      // 表单验证失败，显示错误信息
    }
  })
}
// 处理文件上传变化的方法
function handleUploadChange(files: UploadFile[], qsId: string) {
  uploadedFilesMap.value[qsId] = files
}
const previewVisible = ref(false)
const previewFile = ref<UploadFile>()
function handlePreview(file: UploadFile) {
  previewFile.value = file
  previewVisible.value = true
}
</script>

<template>
  <div class="right-screen">
    <!-- 绑定 formRef 和 rules 属性 -->
    <el-form ref="formRef" :model="form" :rules="rules" label-width="120px" label-position="top">
      <el-form-item v-for="it in questions" :key="it.id" :label="it.question" :prop="`question${it.id}`" required>
        <div class="input-with-upload">
          <el-input
            v-model="form[`question${it.id}`]"
            type="textarea"
            :disabled="disabled || isSubmit"
            :placeholder="it.task_id ? '本问题由AI小O生成' : '请输入答案，体现重点工作进展与近期计划。'"
            maxlength="300"
            show-word-limit
          />
          <UploadFiles
            v-if="!disabled && !isSubmit"
            :data="uploadedFilesMap[it.id]"
            :show-file-list="true"
            class="upload-icon"
            action="/upload"
            accept=".png,.jpg,.jpeg,.gif,.webp"
            :disabled="disabled || isSubmit"
            :limit="3"
            list-type="picture-card"
            :only-img="true"
            url="okr"
            @change="(files) => handleUploadChange(files, it.id)"
            @preview="handlePreview"
          >
            <template #default>
              <el-tooltip class="item" effect="dark" content="添加图片，最多3个" placement="top">
                <el-icon><Upload /></el-icon>
              </el-tooltip>
            </template>
          </UploadFiles>
          <div v-else class="mt-10px">
            <ImagePreview :files="uploadedFilesMap[it.id]?.map(f => f.url)" />
          </div>
        </div>
      </el-form-item>
    </el-form>
    <div class="mt-40px text-center">
      <el-button :disabled="disabled || isSubmit" size="large" type="primary" @click="handleSubmit">
        {{ isSubmit ? '已提交' : '提交' }}
      </el-button>
    </div>
    <el-image-viewer
      v-if="previewVisible"
      :url-list="[previewFile.url]"
      show-progress
      :initial-index="0"
      @close="previewVisible = false"
    />
  </div>
</template>

<style scoped>
.right-screen {
  width: 50%;
  float: right;
  padding: 20px;
  background-color: white;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
}

.input-with-upload {
  position: relative;
  width: 100%;
}

.upload-icon {
  width: 100%;
  margin-top: 10px;
  text-align: right;
  z-index: 1;
}
:deep(.el-upload-list) {
  display: flex;
  flex-wrap: wrap;
}
:deep(.el-upload--picture-card) {
  width: 100px;
  height: 100px;
}
:deep(.el-upload-list__item) {
  width: 100px;
  height: 100px;
}
:deep(.el-upload-list__item-thumbnail) {
  width: 100px;
  height: 100px;
}
</style>
