<script lang="ts" setup>
import type { Objective } from '@/types'
import { onMounted, ref } from 'vue'
import EChartComponent from '@/pages/okr/components/EChartComponent.vue'
import { useChartOptions } from '@/pages/okr/hooks/useChartOptions'

interface Props {
  item: Objective
}

const props = defineProps<Props>()

const chartOptions = ref({})

onMounted(() => {
  chartOptions.value = useChartOptions(props.item)
})
</script>

<template>
  <div class="left-screen">
    <!-- 渲染 ECharts 组件 -->
    <EChartComponent :chart-id="`chartId${item.id}`" :options="chartOptions" />
    <div class="flex gap-12px mt-8px">
      <div class="flex-1 py-4px px-8px flex flex-col items-center bg-#ebf5f6 border-radius-6px mb-10px">
        <div class="text-16px font-700">
          初步改善建议
        </div>
        <div class="text-12px">
          {{ item.aiResults?.suggestion || '初步改善建议' }}
        </div>
      </div>
      <div class="flex-1 py-4px px-8px flex flex-col items-center bg-#fff9eb border-radius-6px mb-10px">
        <div class="text-16px font-700">
          风险预警
        </div>
        <div class="text-12px">
          {{ item.aiResults?.risk || '这里填写风险预警相关内容。' }}
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.left-screen {
  width: 50%;
  padding: 20px;
  background-color: white;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
}
.suggestions,
.risk-warning,
.history-links {
  margin-bottom: 20px;
}
</style>
