<script setup lang="ts">
import type { InjectUser } from '@/hooks/useUser'
import type { AiResult, Objective, Question, Task, User } from '@/types'
import { ArrowDownBold, ArrowUpBold, Refresh } from '@element-plus/icons-vue'
import { uniqueArrayByProperty } from '@mis/shared'
import api from '@mis/shared/src/request'
import { ElMessage, ElMessageBox } from 'element-plus'
import { inject, onMounted, ref } from 'vue'
import ZkhTable from '@/components/zkh-table.vue'
import AddDialog from '../dict/components/add.vue'
import QA from './components/qa.vue'
import { useAiAnalysis } from './hooks/useAiAnalysis'
import { useDify } from './hooks/useDify'
import useTask from './hooks/useTask'
import { formatOkrInfo, handleAIRequestRistAndSuggestion, saveAiResult } from './utils'
import { getSetp2AppKey } from './utils/appKey'

const {
  gridProps,
  gridEvents,
  getPoolList,
  pageInfo,
  formRef,
  form,
  addVisible,
  handleDelete,
  rowInfo,
  actionType,
  handleConfirm,
  handleSendReport,
} = useTask()

const { handleDifyRequest } = useDify()
const { queryAiAnalysis, saveAiAnalysis } = useAiAnalysis()

const foldStack = ref<Record<number, Record<number, boolean>>>({})
const user = inject<InjectUser>('user')
function handleQuery() {
  pageInfo.pageNum = 1
  getPoolList(form.value)
  foldStack.value = {}
}
function handleReset() {
  pageInfo.pageNum = 1
  pageInfo.pageSize = 10
  form.value = {}
  getPoolList(form.value)
  foldStack.value = {}
}
function handlePageChange({
  pageNum,
  pageSize,
}: {
  pageNum: number
  pageSize: number
}) {
  pageInfo.pageNum = pageNum
  pageInfo.pageSize = pageSize
  getPoolList(form.value)
  foldStack.value = {}
}
onMounted(() => {
  getPoolList(form.value)
})
const visible = ref(false)
const taskId = ref(0)
const loading = ref(false)
function handleView(id: number) {
  taskId.value = id
  visible.value = true
}

async function handleAIRequest(row: Task) {
  try {
    loading.value = true
    const res = await api({
      url: `/api-mis/okr/task/${row.id}/objectives/admin`,
      method: 'get',
    })
    if (!res.data) {
      throw new Error('获取OKR列表失败')
    }
    const objectives = res.data as Objective[]
    const users = objectives.map(item => item.users).flat()
    const managers = users.filter((item: User) => item.type === 2) || []
    const managersUername = (uniqueArrayByProperty(managers, 'username') as User[]).map(item => item.username)
    for (const username of managersUername) {
      const aiAnalysisData = await queryAiAnalysis(row.id, username)
      if (aiAnalysisData) {
        continue
      }
      const objs = objectives.filter(item => item.users.some(u => u.username === username && u.type === 2))
      if (objs && objs.length > 0) {
        const queryInfo = objs.map(obj => formatOkrInfo(obj))
        const appKey = getSetp2AppKey()
        await handleDifyRequest(appKey, queryInfo, row.creator, {
          onClose: async (accumulatedText, _) => {
          // 保存AI结果到数据库
            try {
              const obj = JSON.parse(accumulatedText)
              const pastHighlights = obj['过去这一阶段进展较大的工作内容']
              const nextPriorities = obj['接下来一阶段的较为重要工作计划']
              if (!pastHighlights || !nextPriorities) {
                ElMessage.error('AI生成分析内容失败，请稍后再试')
                return
              }
              await saveAiAnalysis({
                task_id: row.id,
                next_priorities: JSON.stringify(nextPriorities) || '',
                past_highlights: JSON.stringify(pastHighlights) || '',
                username,
              })
            }
            catch (error) {
              console.error('保存AI结果时出错:', error)
            }
          },
        })
      }
    }
  }
  catch (error) {
    console.error('处理流式请求时出错:', error)
    throw error
  }
  finally {
    loading.value = false
  }
}
async function handleGenerateReport(row: any) {
  try {
    await handleAIRequest(row)
    ElMessage.success('生成周报成功')
  }
  catch {
    ElMessage.error('生成周报失败，请稍后再试')
  }
}

function handleFold(id: number, idx: number) {
  if (!foldStack.value[id]) {
    foldStack.value[id] = {}
  }
  foldStack.value[id][idx] = !foldStack.value[id][idx]
}

async function handleRefresh(task: Task, question: Question) {
  try {
    loading.value = true
    if (task.ai_results && task.ai_results.length > 0 && question.objective_id) {
      const aiResult = task.ai_results.find((item: AiResult) => item.objective_id === question.objective_id)
      if (aiResult?.id && question.id) {
        const [res1, res2] = await Promise.all([
          api({
            url: `/api-mis/okr/aiResult?ids=${aiResult.id}`,
            method: 'DELETE',
          }),
          api({
            url: `/api-mis/okr/objective/questions?ids=${question.id}`,
            method: 'DELETE',
          }),
        ])

        if (res1.data && res2.data) {
          const res = await api({
            url: `/api-mis/okr/objective/${question.objective_id}`,
            method: 'GET',
          })
          if (res.data) {
            const obj = res.data as Objective
            const { risk, suggestion, question: newQuestion } = await handleAIRequestRistAndSuggestion(obj, user.value.username)
            if (!risk || !suggestion || !newQuestion) {
              ElMessage.error('生成失败，请稍后再试')
              return
            }
            const [res3, res4] = await Promise.all([saveAiResult({
              objective_id: question.objective_id,
              task_id: task.id,
              risk: risk || '',
              suggestion: suggestion || '',
            }), api({
              url: `/api-mis/okr/task/${task.id}/question`,
              method: 'post',
              data: {
                objective_id: question.objective_id,
                question: newQuestion,
              },
            })])
            if (res3.data && res4.data) {
              ElMessage.success('刷新成功')
              getPoolList(form.value)
            }
          }
        }
      }
    }
  }
  catch (error) {
    console.error('刷新失败', error)
  }
  finally {
    loading.value = false
  }
}

function handleSend(row: Task) {
  ElMessageBox.confirm(
    '此操作将会发送企微消息给所有的OKR管理者. 继续?',
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    },
  )
    .then(() => {
      handleSendReport(row.id, row.year)
    })
}
</script>

<template>
  <div v-loading="loading" class="bg-white p-12px min-h-440px">
    <el-card>
      <template #header>
        <div class="text-16px font-700 mb-12px">
          任务管理列表
        </div>
        <el-form ref="formRef" :inline="true">
          <el-form-item label="任务ID" prop="taskId">
            <el-input v-model="form.taskId" placeholder="请输入" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">
              查询
            </el-button>
            <el-button @click="handleReset">
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </template>
      <ZkhTable
        :page-info="pageInfo"
        :grid-config="gridProps"
        :grid-events="gridEvents"
        @page-change="handlePageChange"
      >
        <template #questions="{ row }">
          <div v-for="(question, idx) in row.questions || []" :key="question.id" class="text-[12px]">
            <div class="flex justify-between">
              <div class="flex items-center mb-1">
                <el-button class="mr-1" size="small" type="warning" :icon="Refresh" circle :disabled="!question.question" @click="handleRefresh(row, question)" />
                <div class="font-extrabold color-[#333]">
                  {{ idx + 1 }}: {{ question?.objective?.name }}
                </div>
              </div>
              <span>
                <el-button size="small" :icon="foldStack[row.id]?.[idx] ? ArrowDownBold : ArrowUpBold" link @click="handleFold(row.id, idx)" />
              </span>
            </div>
            <div v-show="foldStack[row.id]?.[idx]" class="color-[#999] h-transition-[height] h-duration-1500 h-ease">
              <div><span class="font-bold color-[#333]">风险: </span>{{ (row.ai_results || []).find(item => item.objective_id === question.objective_id)?.risk || '' }}</div>
              <div><span class="font-bold color-[#333]">建议: </span>{{ (row.ai_results || []).find(item => item.objective_id === question.objective_id)?.suggestion || '' }}</div>
              <div><span class="font-bold color-[#333]">问题：</span>{{ question.question }}</div>
            </div>
          </div>
        </template>
        <template #operate="{ row }">
          <div class="flex items-center mb-1">
            <el-button
              size="small"
              class="w-[90px]"
              plain
              type="primary"
              @click="handleView(row.id)"
            >
              查看
            </el-button>
            <el-button
              class="w-[90px]"
              size="small"
              plain
              type="danger"
              @click="handleDelete([row.id])"
            >
              删除
            </el-button>
          </div>
          <div class="flex items-center">
            <el-button
              :loading="row.isLoading"
              class="w-[90px]"
              size="small"
              plain
              type="primary"
              @click="handleGenerateReport(row)"
            >
              生成周报
            </el-button>
            <el-button
              class="w-[90px]"
              size="small"
              plain
              type="danger"
              @click="handleSend(row)"
            >
              发送周报
            </el-button>
          </div>
        </template>
      </ZkhTable>
    </el-card>
    <AddDialog
      v-model="addVisible"
      :detail="rowInfo"
      :action-type="actionType"
      @confirm="handleConfirm"
    />
    <el-dialog v-if="visible" v-model="visible" width="900px">
      <QA :task-id="taskId" />
    </el-dialog>
  </div>
</template>
