<script setup lang="ts">
import type { Ref } from 'vue'
import type { IObjective } from '../index.vue'
import type { User as IUser } from '@/types'
import { ElMessage } from 'element-plus'
import { computed, inject, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import { useAiAnalysis } from '../../hooks/useAiAnalysis'
import { useDify } from '../../hooks/useDify'
import { formatOkrInfo } from '../../utils'
import { getSetp2AppKey } from '../../utils/appKey'

interface Props {
  taskId: number
  okrListData: IObjective[]
  overviewTime: string
}

const props = defineProps<Props>()

const emit = defineEmits(['update:overviewTime'])

const { handleDifyRequest } = useDify()
const { queryAiAnalysis, saveAiAnalysis } = useAiAnalysis()
const pastHighlights = ref(['正在生成中...'])
const nextPriorities = ref(['正在生成中...'])

const user = inject<Ref<IUser | undefined>>('user')
const username = computed(() => user?.value?.username)
const route = useRoute()
const targetUsername = computed(() => route.query.manager as string)

async function handleAIRequest(okrListData) {
  try {
    const aiAnalysisData = await queryAiAnalysis(props.taskId, targetUsername.value || username.value)
    if (aiAnalysisData) {
      pastHighlights.value = JSON.parse(aiAnalysisData.past_highlights)
      nextPriorities.value = JSON.parse(aiAnalysisData.next_priorities)
      emit('update:overviewTime', aiAnalysisData.gmt_create)
      return
    }
    const queryInfo = okrListData?.map((item: any) => {
      return formatOkrInfo(item)
    })
    const appKey = getSetp2AppKey()
    handleDifyRequest(appKey, queryInfo, username.value, {
      onClose: async (accumulatedText) => {
        // 保存AI结果到数据库
        try {
          console.log('AI结果:', JSON.parse(accumulatedText))
          const obj = JSON.parse(accumulatedText)
          const pastHighlightsValue = obj['过去这一阶段进展较大的工作内容']
          const nextPrioritiesValue = obj['接下来一阶段的较为重要工作计划']
          if (!pastHighlightsValue || !nextPrioritiesValue) {
            ElMessage.error('AI生成分析内容失败，请稍后再试')
            return
          }
          pastHighlights.value = pastHighlightsValue
          nextPriorities.value = nextPrioritiesValue
          const res = await saveAiAnalysis({
            task_id: props.taskId,
            next_priorities: JSON.stringify(nextPrioritiesValue) || '',
            past_highlights: JSON.stringify(pastHighlightsValue) || '',
            username: targetUsername.value || username.value,
          })
          emit('update:overviewTime', new Date(res.gmt_create))
        }
        catch (error) {
          console.error('保存AI结果时出错:', error)
        }
      },
    })
  }
  catch (error) {
    console.error('处理流式请求时出错:', error)
    throw error
  }
}

watch(
  () => props.okrListData,
  (newValue) => {
    if (newValue && newValue.length > 0) {
      handleAIRequest(newValue)
    }
  },
  { immediate: true }, // 立即执行一次
)
</script>

<template>
  <div class="flex mb-6 bg-[#f0f4f9] rounded-8px p-4">
    <div class="flex-1 px-3 relative">
      <h3 class="text-gray-700 mb-4">
        上期重点工作事项
      </h3>
      <div
        v-for="(item, index) in pastHighlights" :key="`highlight-${index}`"
        class="bg-white p-3 mb-3 rounded-6px min-h-40px"
      >
        {{ item || '暂无数据' }}
      </div>
    </div>
    <div class="flex-1 px-3 relative">
      <h3 class="text-gray-700 mb-4">
        本期及下期工作计划
      </h3>
      <div
        v-for="(item, index) in nextPriorities" :key="`proriority-${index}`"
        class="bg-white p-3 mb-3 rounded-6px min-h-40px"
      >
        {{ item || '暂无数据' }}
      </div>
      <div class="text-right text-12px text-gray-500 mt-2">
        以上内容由AI小O生成
      </div>
    </div>
  </div>
</template>
