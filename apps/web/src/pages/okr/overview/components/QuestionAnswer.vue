<script setup lang="ts">
import type { Question, User } from '@/types'
import ImagePreview from '@/components/ImagePreview.vue'

defineProps<{
  questions?: Question[]
  users?: User[]
}>()
</script>

<template>
  <div v-for="(question, index) in questions || []" :key="`question-${index}`" class="mb-6">
    <div class="font-bold mb-3">
      {{ question.question }}
      <el-tag v-if="question.task_id" class="ml-1" size="small" type="danger">
        AI
      </el-tag>
    </div>
    <div v-if="question.answers && question.answers.length" class="flex flex-col gap-3">
      <div
        v-for="(answer, aIndex) in question.answers" :key="`answer-${index}-${aIndex}`"
        class="flex flex-col border-solid border-gray-200 rounded-8px p-4 flex justify-between"
      >
        <div class="flex-1 pr-2">
          <div class="font-bold mb-2">
            答案{{ aIndex + 1 }}
          </div>
          <div class="text-gray-500 break-words">
            {{ answer.content }}
          </div>
          <ImagePreview v-if="answer.files" :files="answer.files.split(',')" />
        </div>
        <div class="flex justify-end mt-2">
          <div class="flex items-center bg-gray-100 px-2 py-1 rounded-16px h-fit whitespace-nowrap">
            <el-icon class="mr-1">
              <User />
            </el-icon>
            <span class="text-12px">{{ answer.nickname }}</span>
          </div>
        </div>
      </div>
    </div>
    <div v-else class="flex flex-col border-solid border-gray-200 rounded-8px p-4 flex justify-between">
      暂无答案
    </div>
  </div>
</template>
