<script setup lang="ts">
import type { Objective } from '@/types'
import EChartComponent from '@/pages/okr/components/EChartComponent.vue'
import { useChartOptions } from '@/pages/okr/hooks/useChartOptions'

interface Props {
  objective: Objective
}

const props = defineProps<Props>()

const chartOptions = useChartOptions(props.objective)
</script>

<template>
  <div class="w-1/2 border border-gray-200 rounded-6px mb-4">
    <EChartComponent :chart-id="`chartId${objective.id}`" :options="chartOptions" />
    <div class="mt-4 p-4 bg-gradient-to-b from-white to-gray-50 rounded-8px border border-gray-200 shadow-md">
      <h3 class="color-#6456b7d4 font-800 mb-4">
        {{ objective.name }}
      </h3>
      <div class="break-words text-gray-500 text-14px mb-4">
        <div><span class="text-gray-700">初步改善建议：</span>{{ objective.aiResults?.suggestion }}</div>
        <div><span class="text-gray-700">风险预警：</span>{{ objective.aiResults?.risk }}</div>
      </div>
      <div class="text-right text-12px font-600 color-#6456b7d4">
        以上内容由AI小O生成
      </div>
    </div>
  </div>
</template>
