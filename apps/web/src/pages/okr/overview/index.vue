<script setup lang="ts">
import type { Ref } from 'vue'
import type { User as IUser, Objective } from '@/types'
import { Download, User } from '@element-plus/icons-vue'
import api from '@mis/shared/src/request'
import dayjs from 'dayjs'
import _ from 'lodash'
import Nzh from 'nzh'
import { computed, inject, onMounted, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import router from '@/router'
import AiAnalysis from './components/AiAnalysis.vue'
import OkrChartCard from './components/OkrChartCard.vue'
import QuestionAnswer from './components/QuestionAnswer.vue'

export interface IObjective extends Objective {
  aiResults?: {
    nextProriorities: string
    pastHighlights: string
    question?: string
  }
  tier1: string
  tier2: string
}

interface OkrList {
  tier1: string
  tier2Groups: {
    tier1: string
    tier2: string
    items: IObjective[]
  }[]
}

const nzhcn = Nzh.cn
const user = inject<Ref<IUser | undefined>>('user')
const username = computed(() => user?.value?.username)
const route = useRoute()
const taskId = computed(() => Number(route.params.id))
const targetUsername = computed(() => route.query.manager)
const year = computed(() => Number(route.query.year) || dayjs().year())
const downloadLoading = ref(false)

const overviewTime = ref<string>(dayjs().format('YYYY-MM-DD HH:mm:ss'))

// 格式化日期和时间
function formatDate(date: string) {
  return dayjs(date).format('MMM D, YYYY')
}

function formatTime(date: string) {
  return dayjs(date).format('h:mm A')
}

const okrListData = ref<IObjective[]>([])
const okrList = ref<OkrList[]>([])

const nickname = computed(() => {
  const currentUsername = targetUsername.value || username.value
  let nickname = currentUsername
  okrListData.value.find((item) => {
    return item.users.find((objectiveUser) => {
      if (objectiveUser.username === currentUsername) {
        nickname = objectiveUser.nickname
      }
      return objectiveUser.username === currentUsername
    })
  })
  return nickname
})

async function fetchAiResults(okrListData: IObjective[]) {
  if (!okrListData || okrListData.length === 0)
    return
  const objectiveIds = okrListData.map(item => item.id)
  const aiResults = await api({
    url: '/api-mis/okr/aiResult/all',
    method: 'POST',
    data: { taskId: taskId.value, objectiveIds },
  })
  if (aiResults?.data) {
    return aiResults.data
  }
  return []
}

// 根据tier1和tier2聚合OKR列表
function groupOkrList(list: IObjective[]): OkrList[] {
  if (!list || !Array.isArray(list) || list.length === 0)
    return []

  // 使用lodash进行分组
  const groupedByTier1 = _.groupBy(list, 'tier1')

  return Object.entries(groupedByTier1).map(([tier1, tier1Items]) => {
    // 按tier2进行二级分组
    const groupedByTier2 = _.groupBy(tier1Items, 'tier2')

    // 转换tier2分组为数组
    const tier2Groups = Object.entries(groupedByTier2).map(([tier2, items]) => ({
      tier1,
      tier2,
      items: items as IObjective[],
    }))

    return {
      tier1,
      tier2Groups,
    }
  })
}

async function queryOkrList() {
  try {
    const res = await api({
      url: `/api-mis/okr/task/${taskId.value}/objectives`,
      method: 'get',
    })
    if (res.data) {
      // 1. 先检查当前用户的管理员权限
      const hasAdminRights = res.data.some(item =>
        item.users.some(u =>
          u.username === username.value
          && u.type === 0
          && u.is_deleted === 0,
        ),
      )
      if (hasAdminRights && targetUsername.value) {
        // 如果是管理员
        return res.data.filter(item =>
          // item.users.some(u => u.username === username.value && u.type === 0) &&
          item.users.some(u => u.username === targetUsername.value && u.type === 2),
        )
      }
      else {
        // 查看自己的记录
        return res.data.filter(item =>
          item.users.some(u =>
            u.username === username.value
            && u.type === 2,
          ),
        )
      }
    }
    else {
      return []
    }
  }
  catch (error) {
    console.error('查询OKR列表时出错:', error)
    return []
  }
}
async function initData() {
  try {
    okrListData.value = await queryOkrList()
    okrListData.value = okrListData.value.filter((item: Objective) => item.year === year.value)
    const [aiResults, ,] = await Promise.all([fetchAiResults(okrListData.value), initHistory()])
    if (okrListData.value?.length) {
      okrListData.value.forEach((item) => {
        const relatedAiResult = aiResults.find((ai: any) => ai.objective_id === item.id)
        item.aiResults = relatedAiResult
      })
      okrList.value = groupOkrList(okrListData.value)
      console.log('okrList', okrList.value)
    }
  }
  catch (err) {
    console.error(err)
  }
}

async function handleDownload() {
  const url = location.href
  try {
    downloadLoading.value = true
    const res = await api({
      url: '/api-mis/token',
      method: 'GET',
    })
    const { accessToken, refreshToken } = res?.data || {}
    if (accessToken || refreshToken) {
      const response = await fetch('/api-fe-server/api/url2pdf', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ url, accessToken, refreshToken }),
      })
      if (response.ok) {
        const blob = await response.blob()
        const newWindow = window.open('', '_blank')
        if (newWindow) {
          newWindow.location.href = URL.createObjectURL(blob)
        }
      }
    }
  }
  catch (error) {
    console.error('Error downloading PDF:', error)
  }
  finally {
    downloadLoading.value = false
  }
}

function changeOverview(item) {
  router.push({
    path: `/okr/overview/${item.task_id}`,
    query: {
      ...route.query,
    },
  })
}

const historyList = ref([])
async function initHistory() {
  const res = await api({
    url: '/api-mis/okr/aiAnalysis/history',
    method: 'get',
    params: {
      username: targetUsername.value || username.value,
    },
  })
  if (res.data) {
    // 使用 lodash 按 task_id 分组，并只保留每组最新的记录
    const groupedByTask = _.groupBy(res.data, 'task_id')
    historyList.value = Object.values(groupedByTask).map((group) => {
      // 按创建时间排序，取最新的一条
      return _.maxBy(group, item => new Date(item.gmt_create).getTime())
    })

    // 最后按创建时间倒序排列
    historyList.value = _.orderBy(historyList.value, [item => new Date(item.gmt_create).getTime()], ['desc'],
    )
  }
}

watch(
  () => route.params.id,
  async (newId) => {
    if (newId) {
      initData()
    }
  },
)

onMounted(() => {
  initData()
})
</script>

<template>
  <div class="mx-20px bg-white rounded-18px shadow-md overflow-hidden">
    <!-- 报告头部 -->
    <div class="flex justify-between items-center p-4 border-solid border-b-3 border-gray-100">
      <div class="">
        <div class="flex items-center bg-#6456b796 text-white p-2 rounded-8px">
          <el-icon class="mr-2 text-20px">
            <User />
          </el-icon>
          <div>
            <div>Report Reviewer</div>
            <div>{{ nickname }}</div>
          </div>
        </div>
      </div>
      <div class="overview-select flex items-center">
        <el-button type="success" :icon="Download" circle :loading="downloadLoading" @click="handleDownload" />
        <div class="mx-2 px-2 bg-#f3f4f5 rounded-2px color-#2196f3">
          {{ formatDate(overviewTime) }}
        </div>
        <div class="px-2 bg-#f3f4f5 rounded-2px color-#2196f3">
          {{ formatTime(overviewTime) }}
        </div>
        <el-select-v2
          v-model="overviewTime"
          :options="historyList"
          :props="{
            label: 'gmt_create',
            value: 'gmt_create',
          }"
          placeholder="请选择报告时间"
          class="ml-4"
          popper-class="history-select-dropdown"
          :teleported="false"
        >
          <template #default="{ item }">
            <div class="flex" @click="changeOverview(item)">
              <span class="text-14px">{{ formatDate(item.gmt_create) }}</span>
              <span class="text-14px ml-15px">{{ formatTime(item.gmt_create) }}</span>
            </div>
          </template>
        </el-select-v2>
      </div>
    </div>

    <!-- 报告内容 -->
    <div class="p-6">
      <AiAnalysis v-model:overview-time="overviewTime" :task-id="taskId" :okr-list-data="okrListData" />
      <!-- 进展和风险部分 -->
      <div v-for="(tier1Group, tier1Index) in okrList" :key="`tier1-${tier1Index}`" class="flex flex-col p-4">
        <div class="mb-3 text-gray-700 color-#6456b7d4">
          <div class="text-18px font-800 mb-2">
            {{ `${nzhcn.encodeS(tier1Index + 1)}、${tier1Group.tier1}` }}
          </div>
          <div v-for="(tier2Group, tier2Index) in tier1Group.tier2Groups" :key="`tier2-${tier2Index}`">
            <div class="bg-#e9ebee4d p-4 rounded-8px mb-4">
              <div class="font-600">
                {{ `${tier2Index + 1}. ${tier2Group.tier2}` }}
              </div>
              <div v-for="(item, index) in tier2Group.items" :key="`item-${index}`">
                <div class="flex flex-row">
                  <!-- OKR卡片 -->
                  <OkrChartCard :objective="(item as Objective)" />
                  <!-- 问题答案部分 -->
                  <div class="w-1/2 pl-4">
                    <QuestionAnswer :questions="item.questions" :users="item.users" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
:deep(.el-select) {
  width: auto !important;
}

:deep(.el-select__wrapper) {
  box-shadow: none !important;
  padding: 0 !important;
}

:deep(.el-popper) {
  min-width: 240px !important;
}

:deep(.el-select-dropdown__list) {
  width: 238px !important;
}
</style>
