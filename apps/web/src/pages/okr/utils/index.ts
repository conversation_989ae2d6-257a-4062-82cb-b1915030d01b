import type { Objective } from '@/types'
import api from '@mis/shared/src/request'
import { handleDifyRequest } from '@/utils/dify'
import { getSetp1AppKey } from './appKey'

// 提取风险和建议的函数
export function extractRiskAndSuggestion(text: string) {
  const result = {
    risk: '',
    suggestion: '',
    question: '',
  }
  if (typeof text !== 'string')
    return result

  // 风险预警
  const riskMatch = text.match(/风险预警：([\s\S]*?)(?=(\n\n)?改善建议：|$)/)
  if (riskMatch && riskMatch[1]) {
    result.risk = riskMatch[1].trim()
  }

  // 改善建议
  const suggestionMatch = text.match(/改善建议：([\s\S]*?)(?=(\n\n)?提问引导：|$)/)
  if (suggestionMatch && suggestionMatch[1]) {
    result.suggestion = suggestionMatch[1].trim()
  }
  // 提问引导
  const questionMatch = text.match(/提问引导：([\s\S]*?)(?=(\n\n)|$)/)
  if (questionMatch && questionMatch[1]) {
    result.question = questionMatch[1].trim()
  }
  return result
}

export function formatOkrInfo(item: Objective) {
  return {
    本周信息: {
      OKR名称: item?.name || '',
      目标值: item?.records?.map(r => r.value).join(', ') || '',
      单位: item?.unit_name || '',
      固定问题: item?.questions?.map(q => q.question).join(', ') || '',
      关键动作: item?.action || '',
      OKR负责人: item?.users?.filter(u => u.type === 1).map(u => u.nickname).join(', ') || '',
      OKR管理者: item?.users?.filter(u => u.type === 2).map(u => u.nickname).join(', ') || '',
      问答内容: item?.questions?.filter(q => q.answers)?.flatMap(q => q.answers)?.map(a => a.content)?.join(', ') || '',
    },
    历史信息: {
      其他信息: '这里放图片ocr的内容',
    },
  }
}

export async function saveAiResult(data) {
  try {
    const res = await api({
      url: '/api-mis/okr/aiResult/create',
      method: 'post',
      data,
    })

    if (res?.data) {
      console.warn('AI结果已成功保存到数据库', res.data)
      return res
    }
    else {
      console.warn('保存AI结果失败：没有返回数据')
      return null
    }
  }
  catch (error) {
    console.error('保存AI结果时出错:', error)
    throw error
  }
}

export async function handleAIRequestRistAndSuggestion(item: Objective, username: string) {
  try {
    // 构建完整的请求参数
    const queryInfo = formatOkrInfo(item)

    const aiResult = {
      risk: '正在生成风险分析...',
      suggestion: '正在生成建议...',
      question: '正在生成问题...',
    }
    const appKey = getSetp1AppKey()
    const { text } = await handleDifyRequest(appKey, queryInfo, username)
    const extracted = extractRiskAndSuggestion(text)
    if (extracted) {
      aiResult.risk = extracted.risk
      aiResult.suggestion = extracted.suggestion
      aiResult.question = extracted.question
    }

    return aiResult
  }
  catch (error) {
    console.error('处理流式请求时出错:', error)
    // throw error
  }
}
