export const columnFields: Array<Record<string, any>> = [
  {
    field: 'id',
    title: 'ID',
    width: '80',
  },
  {
    field: 'year',
    title: '财年',
    width: '80',
  },
  {
    field: 'tier1',
    title: 'Tier1',
    width: '10%',
  },
  {
    field: 'tier2',
    title: 'Tier2',
    width: '10%',
  },
  {
    field: 'name',
    title: 'OKR名称',
    width: '12%',
  },
  {
    field: 'admin',
    title: '管理员',
    width: '8%',
    slots: {
      default: 'admin',
    },
  },
  {
    field: 'target',
    title: '目标值',
    width: '12%',
    slots: {
      default: 'target',
    },
  },
  {
    field: 'achieve',
    title: '实际值',
    width: '12%',
    slots: {
      default: 'achieve',
    },
  },
  {
    field: 'unit_name',
    title: '单位',
    width: '7%',
  },
  {
    field: 'question',
    title: '问题',
    width: '16%',
    slots: {
      default: 'question',
    },
  },
  {
    field: 'action',
    title: '关键动作',
    width: '14%',
  },
  {
    field: 'owner',
    title: 'OKR负责人',
    width: '10%',
    slots: {
      default: 'owner',
    },
  },
  {
    field: 'manager',
    title: 'OKR管理者',
    width: '10%',
    slots: {
      default: 'manager',
    },
  },
  {
    field: 'creator',
    title: '创建人',
    width: '100',
  },
  {
    field: 'updater',
    title: '修改人',
    width: '100',
  },
  {
    field: 'operate',
    title: '操作',
    width: '240',
    align: 'center',
    fixed: 'right',
    slots: {
      default: 'operate',
    },
  },
]
export const urlConfig = {
  list: '/api-mis/okr/objectives',
  add: '/api-mis/okr/objective',
  update: '/api-mis/okr/objective',
  delete: '/api-mis/okr/objectives',
}

export const recordColumns: Array<Record<string, any>> = [
  {
    field: 'showVal',
    title: '目标值',
    width: '200',
  },
  {
    field: 'creator',
    title: '创建人',
    width: '100',
  },
  {
    field: 'updater',
    title: '修改人',
    width: '100',
  },
  {
    field: 'operate',
    title: '操作',
    align: 'center',
    fixed: 'right',
    slots: {
      default: 'operate',
    },
  },
]

export const achieveColumns: Array<Record<string, any>> = [
  {
    field: 'showVal',
    title: '目标值',
    width: '200',
  },
  {
    field: 'creator',
    title: '创建人',
    width: '100',
  },
  {
    field: 'updater',
    title: '修改人',
    width: '100',
  },
  {
    field: 'operate',
    title: '操作',
    align: 'center',
    fixed: 'right',
    slots: {
      default: 'operate',
    },
  },
]

export const questionColumns: Array<Record<string, any>> = [
  {
    field: 'question',
    title: '固定问题',
    width: '200',
  },
  {
    field: 'creator',
    title: '创建人',
    width: '100',
  },
  {
    field: 'updater',
    title: '修改人',
    width: '100',
  },
  {
    field: 'operate',
    title: '操作',
    align: 'center',
    fixed: 'right',
    slots: {
      default: 'operate',
    },
  },
]

export const okrAdminColumns: Array<Record<string, any>> = [
  {
    field: 'nickname',
    title: 'OKR管理员',
    width: '200',
  },
  {
    field: 'creator',
    title: '创建人',
    width: '100',
  },
  {
    field: 'updater',
    title: '修改人',
    width: '100',
  },
  {
    field: 'operate',
    title: '操作',
    align: 'center',
    fixed: 'right',
    slots: {
      default: 'operate',
    },
  },
]

export const okrOwnerColumns: Array<Record<string, any>> = [
  {
    field: 'nickname',
    title: 'OKR负责人',
    width: '200',
  },
  {
    field: 'creator',
    title: '创建人',
    width: '100',
  },
  {
    field: 'updater',
    title: '修改人',
    width: '100',
  },
  {
    field: 'operate',
    title: '操作',
    align: 'center',
    fixed: 'right',
    slots: {
      default: 'operate',
    },
  },
]

export const okrManagerColumns: Array<Record<string, any>> = [
  {
    field: 'nickname',
    title: 'OKR管理者',
    width: '200',
  },
  {
    field: 'creator',
    title: '创建人',
    width: '100',
  },
  {
    field: 'updater',
    title: '修改人',
    width: '100',
  },
  {
    field: 'operate',
    title: '操作',
    align: 'center',
    fixed: 'right',
    slots: {
      default: 'operate',
    },
  },
]

export const updateAPIFetch = {
  delRecords: '/api-mis/okr/objective/records',
  delQuestions: '/api-mis/okr/objective/questions',
  delUsers: '/api-mis/okr/objective/users',
  addRecords: '/api-mis/okr/newRecord',
  addQuestions: '/api-mis/okr/newQuestion',
  addUsers: '/api-mis/okr/newUser',
  updateRecords: '/api-mis/okr/objective/records',
  updateQuestions: '/api-mis/okr/objective/questions',
  updateUsers: '/api-mis/okr/objective/users',
}

export const labelMap = {
  record: '目标值',
  achieve: '实际值',
  question: '固定问题',
  okrAdmin: 'OKR管理员',
  okrOwner: 'OKR负责人',
  okrManager: 'OKR管理者',
}

export interface okrFields {
  id?: number
  tier1?: string
  tier2?: string
  name?: string
  records?: Array<Record<string, any>>
  unint?: number
  questions?: Array<Record<string, any>>
  action?: string
  users?: Array<Record<string, any>>
}
export const urlConfigTask = {
  list: '/api-mis/okr/task/list',
  add: '/api-mis/okr/task',
  update: '/api-mis/okr/task',
  delete: '/api-mis/okr/tasks',
}

export const columnFieldsTask: Array<Record<string, any>> = [
  {
    field: 'id',
    title: 'ID',
    width: '80',
  },
  {
    field: 'year',
    title: '财年',
    width: '80',
  },
  {
    field: 'questions',
    title: 'AI生成内容',
    align: 'left',
    slots: {
      default: 'questions',
    },
  },
  {
    field: 'gmt_create',
    title: '创建时间',
    width: '170',
  },
  {
    field: 'gmt_modified',
    title: '修改时间',
    width: '170',
  },
  {
    field: 'creator',
    title: '创建人',
    width: '100',
  },
  {
    field: 'updater',
    title: '修改人',
    width: '100',
  },
  {
    field: 'oprate',
    title: '操作',
    width: '200',
    align: 'center',
    fixed: 'right',
    slots: {
      default: 'operate',
    },
  },
]

export const columnFieldsJob: Array<Record<string, any>> = [
  {
    field: 'id',
    title: 'ID',
    width: '80',
  },
  {
    field: 'task_id',
    title: '所属任务ID',
    width: '160',
  },
  {
    field: 'username',
    title: '用户名',
  },
  {
    field: 'nickname',
    title: '中文名',
  },
  {
    field: 'gmt_create',
    title: '创建时间',
  },
  {
    field: 'gmt_modified',
    title: '修改时间',
  },
  {
    field: 'oprate',
    title: '操作',
    width: '168',
    align: 'center',
    fixed: 'right',
    slots: {
      default: 'operate',
    },
  },
]

export const urlConfigJob = {
  update: '/api-mis/okr/tajobsk',
  delete: '/api-mis/okr/jobs',
}

export const columnFieldsJobDetail: Array<Record<string, any>> = [
  {
    field: 'jobId',
    title: 'jobId',
    width: '80',
    slots: {
      default: 'jobId',
    },
  },
  {
    field: 'task_id',
    title: '任务ID',
    width: '100',
  },
  {
    field: 'question',
    title: '问题',
    slots: {
      default: 'question',
    },
  },
  {
    field: 'answer',
    title: '答案',
    slots: {
      default: 'answer',
    },
  },
  {
    field: 'okr_name',
    title: '所属OKR',
  },
  {
    field: 'user_name',
    title: '提交人',
    width: '160',
  },
  {
    field: 'gmt_create',
    title: '提交时间',
    slots: {
      default: 'gmt_create',
    },
  },
  {
    field: 'operate',
    title: '操作',
    width: '88',
    align: 'center',
    fixed: 'right',
    slots: {
      default: 'operate',
    },
  },
]

export const urlConfigJobDetail = {
  list: '/api-mis/okr/objective/answers/all',
  // add: "/api-mis/okr/job",
  delete: '/api-mis/okr/objective/answers',
}
