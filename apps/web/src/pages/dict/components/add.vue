<script setup lang="ts">
import { reactive, ref, watch } from 'vue'

interface FormField {
  value: number
  type: number
  id: number
  name: string
  module: string
}
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  actionType: {
    type: String,
    default: 'add',
  },
  detail: {
    type: Object,
    default: () => ({}),
  },
})
const emit = defineEmits(['update:modelValue', 'confirm'])
const rules = reactive({
  value: [{ required: true, message: '请输入value', trigger: 'blur' }],
  type: [{ required: true, message: '请输入类型', trigger: 'blur' }],
  name: [{ required: true, message: '请输入name', trigger: 'blur' }],
  module: [{ required: true, message: '请输入模块', trigger: 'blur' }],
})
const formRef = ref<any>(null)
const formField = reactive<FormField>({
  value: props.detail.value,
  type: props.detail.type,
  id: props.detail.id,
  name: props.detail.name || '',
  module: props.detail.module || '',
})
const show = ref(props.modelValue)
function handleConfirm() {
  formRef.value
    .validate()
    .then(() => {
      emit('confirm', formField)
    })
    .catch((err: any) => {
      console.error(err)
    })
}
watch(
  () => props.modelValue,
  (val) => {
    show.value = val
    formField.id = props.detail.id
    formField.name = props.detail.name || ''
    formField.value = props.detail.value
    formField.type = props.detail.type
    formField.module = props.detail.module || ''
  },
)
</script>

<template>
  <el-dialog
    v-model="show"
    :title="`${actionType === 'add' ? '新增' : '编辑'}Dict`"
    @click.stop
    @close="emit('update:modelValue', false)"
  >
    <div class="px-32px pt-12px pb-2px">
      <el-form
        ref="formRef"
        :model="formField"
        :rules="rules"
        :label-width="100"
      >
        <el-form-item v-if="actionType === 'edit'" label="ID" prop="id">
          <el-input v-model="formField.id" disabled placeholder="请输入ID" />
        </el-form-item>
        <el-form-item label="名称" prop="name">
          <el-input v-model="formField.name" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="值" prop="value">
          <el-input v-model="formField.value" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="type" prop="type">
          <el-input v-model="formField.type" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="module" prop="module">
          <el-input v-model="formField.module" placeholder="请输入" />
        </el-form-item>
      </el-form>
    </div>
    <div class="flex justify-center mt-24px pb-12px">
      <el-button @click="emit('update:modelValue', false)">
        取消
      </el-button>
      <el-button type="primary" @click="handleConfirm">
        完成
      </el-button>
    </div>
  </el-dialog>
</template>
