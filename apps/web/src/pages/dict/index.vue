<script setup lang="ts">
import { onMounted } from 'vue'
import ZkhTable from '@/components/zkh-table.vue'
import AddDialog from './components/add.vue'
import { urlConfigDict } from './constants'
import useDict from './hooks/useDict'

const {
  gridProps,
  gridEvents,
  getPoolList,
  pageInfo,
  formRef,
  form,
  addVisible,
  handleAdd,
  handleDelete,
  handleEdit,
  rowInfo,
  actionType,
  handleConfirm,
} = useDict(urlConfigDict)
function handleQuery() {
  pageInfo.pageNum = 1
  getPoolList(form.value)
}
function handleReset() {
  pageInfo.pageNum = 1
  pageInfo.pageSize = 10
  form.value = {}
  getPoolList(form.value)
}
function handlePageChange({
  pageNum,
  pageSize,
}: {
  pageNum: number
  pageSize: number
}) {
  pageInfo.pageNum = pageNum
  pageInfo.pageSize = pageSize
  getPoolList(form.value)
}
onMounted(() => {
  getPoolList(form.value)
})
</script>

<template>
  <div class="bg-white p-12px min-h-440px">
    <el-card>
      <template #header>
        <div class="text-16px font-700 mb-12px">
          Dict管理列表
        </div>
        <el-form ref="formRef" class="flex justify-between">
          <el-form-item label="名称" prop="name">
            <el-input v-model="form.name" placeholder="请输入名称" @keyup.enter="handleQuery" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleQuery">
              查询
            </el-button>
            <el-button @click="handleReset">
              重置
            </el-button>
            <el-button type="primary" @click="handleAdd({})">
              创建
            </el-button>
          </el-form-item>
        </el-form>
      </template>
      <ZkhTable
        :page-info="pageInfo"
        :grid-config="gridProps"
        :grid-events="gridEvents"
        @page-change="handlePageChange"
      >
        <template #operate="{ row }">
          <el-button
            size="small"
            plain
            type="primary"
            @click="handleEdit(row, 'edit')"
          >
            编辑
          </el-button>
          <el-button
            size="small"
            plain
            type="danger"
            @click="handleDelete([row.id])"
          >
            删除
          </el-button>
        </template>
      </ZkhTable>
    </el-card>
    <AddDialog
      v-model="addVisible"
      :detail="rowInfo"
      :action-type="actionType"
      @confirm="handleConfirm"
    />
  </div>
</template>
