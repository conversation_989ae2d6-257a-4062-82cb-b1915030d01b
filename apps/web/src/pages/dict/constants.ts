import type { VxeGridPropTypes } from 'vxe-table'

export const columnFieldsDict: VxeGridPropTypes.Columns = [
  {
    field: 'id',
    title: 'ID',
    width: '80',
  },
  {
    field: 'name',
    title: '名称',
  },
  {
    field: 'value',
    title: '值',
  },
  {
    field: 'type',
    title: '类型',
  },
  {
    field: 'module',
    title: '模块',
  },
  {
    field: 'creator',
    title: '创建人',
  },
  {
    field: 'updater',
    title: '更新人',
  },
  {
    field: 'operate',
    title: '操作',
    width: '168',
    align: 'center',
    fixed: 'right',
    slots: {
      default: 'operate',
    },
  },
]
export const urlConfigDict = {
  list: '/api-mis/dict/list',
  add: '/api-mis/dict',
  update: '/api-mis/dict',
  delete: '/api-mis/dict',
}
