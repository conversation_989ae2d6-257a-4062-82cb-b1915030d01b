export const columnFields: Array<Record<string, any>> = [
  {
    field: 'tier1',
    title: 'Tier1',
    width: '10%',
  },
  {
    field: 'tier2',
    title: 'Tier2',
    width: '10%',
  },
  {
    field: 'name',
    title: 'OKR名称',
    width: '12%',
  },
  {
    field: 'createdName',
    title: '目标值',
    width: '16%',
  },
  {
    field: 'unit',
    title: '单位',
    width: '7%',
  },
  {
    field: 'approveStatusName',
    title: '固定问题',
    width: '16%',
    slots: {
      default: 'status',
    },
  },
  {
    field: 'action',
    title: '关键动作',
    width: '14%',
  },
  {
    field: 'approveTime',
    title: 'OKR负责人',
    width: '10%',
  },
  {
    field: 'approveUserName',
    title: 'OKR管理者',
    width: '10%',
  },
  {
    field: 'operate',
    title: '操作',
    width: '168',
    align: 'center',
    fixed: 'right',
    slots: {
      default: 'operate',
    },
  },
]
export const urlConfig = {
  list: '/api-mis/okr/objectives',
}
