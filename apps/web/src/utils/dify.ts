export async function handleDifyRequest(appKey: string, queryInfo: unknown, username: string) {
  const requestParams = {
    inputs: {
      info: JSON.stringify(queryInfo),
    },
    response_mode: 'blocking',
    user: username,
  }

  const response = await fetch('/api-agent/v1/workflows/run', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${appKey}`,
    },
    body: JSON.stringify(requestParams),
  })

  if (!response.ok) {
    throw new Error(`请求失败: ${response.status}`)
  }

  const data = await response.json()

  // 处理返回的数据
  const text = data.data?.outputs?.text || ''

  return { text }
}
