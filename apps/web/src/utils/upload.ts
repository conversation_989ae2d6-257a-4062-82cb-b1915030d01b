import type { UploadRawFile } from 'element-plus/lib/components/upload'
import OSS from 'ali-oss'
import axios from 'axios'

export interface ResponseType {
  objectKey: string
  fileName: string
  url: string
}

const isPro = /boss.zkh360.com/.test(location.href)
const region = 'oss-cn-beijing'
let bucket = 'zkh360-boss'
let fileDomain = 'https://files.zkh360.com'
if (!isPro) {
  bucket = 'zkh360-boss-uat'
  fileDomain = 'https://files-uat.zkh360.com'
}
const prefix = 'mis'
// 阿里云oss client
let client: OSS | null = null
export async function getClient() {
  try {
    if (client) {
      return client
    }
    const result = await axios.get('/api-oss/sts')
    if (result && result.data) {
      const {
        AccessKeyId,
        AccessKeySecret,
        SecurityToken,
      } = result.data as Record<string, string>
      client = new OSS({
        region,
        bucket,
        accessKeyId: AccessKeyId,
        accessKeySecret: AccessKeySecret,
        stsToken: SecurityToken,
        refreshSTSToken: async () => {
          const refreshSTSResponse = await axios.get(
            '/api-oss/sts',
          )
          if (refreshSTSResponse && refreshSTSResponse.data) {
            const {
              AccessKeyId,
              AccessKeySecret,
              SecurityToken,
            } = refreshSTSResponse.data as Record<string, string>
            return {
              accessKeyId: AccessKeyId,
              accessKeySecret: AccessKeySecret,
              stsToken: SecurityToken,
            }
          }
          throw new Error('fail to get oss sts token!')
        },
      })
      return client
    }
  }
  catch (e) {
    console.error('getClient error', e)
  }
  return null
}

export function getOSSObject(file: UploadRawFile, path: string, customFileName?: string) {
  const arr = file.name.split('.')
  const len = arr.length
  let objectKey = ''
  if (len >= 2) {
    const postfix = arr[len - 1]
    arr.pop()
    const name = arr.join('')
    const ossFileName = customFileName
      ? customFileName.includes(postfix)
        ? customFileName
        : `${customFileName}.${postfix}`
      : `${name}.${postfix}`
    objectKey = path
      ? `${prefix}/${path}/${ossFileName}`
      : `${prefix}/${ossFileName}`
  }
  return {
    objectKey,
    fileName: file.name,
  }
}
/**
 * @param {*} path 上传文件路径
 * @param {*} file 上传文件
 * @param {*} customFileName 自定义文件名
 * @returns 上传成功后可预览路径
 */
export async function upload(
  file: UploadRawFile,
  path: string,
  customFileName?: string,
): Promise<ResponseType> {
  let url = ''
  const { objectKey, fileName } = getOSSObject(file, path, customFileName)
  if (objectKey) {
    try {
      const client = await getClient()
      const response = await client?.put(objectKey, file)
      if (response?.url) {
        const urlPath = new URL(response.url)
        const path = urlPath.pathname
        const regex = /\.(?:jpg|jpeg|png|gif|webp)$/i
        if (regex.test(fileName)) {
          url = `${fileDomain}${path}`
        }
        else {
          url = `${fileDomain}${path}`
        }
      }
    }
    catch (ex) {
      console.error(ex)
    }
  }
  return {
    objectKey,
    fileName,
    url,
  }
}
