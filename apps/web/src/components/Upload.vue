<script setup lang="ts">
import type { UploadProps } from 'element-plus'
import type {
  UploadFile,
  UploadRawFile,
  UploadRequestOptions,
} from 'element-plus/lib/components/upload'
import { ElMessage } from 'element-plus'
import { ref, watch } from 'vue'
import { upload } from '@/utils/upload'

const props = defineProps({
  data: { type: Array, default: () => [] },
  url: { type: String, default: () => '' },
  class: { type: String, default: () => '' },
  drag: { type: Boolean, default: () => false },
  accept: { type: String, default: () => '' },
  multiple: { type: Boolean, default: () => false },
  limit: { type: Number, default: () => 999 },
  listType: { type: String, default: () => 'text' },
  customFileName: { type: String },
  onlyImg: { type: Boolean, default: () => false },
  validate: { type: Function, default: () => true },
  maxSize: { type: Number, default: () => 10 },
})

const emit = defineEmits([
  'change',
  'start',
  'remove',
  'preview',
  'beforeUpload',
])
const data = ref(props.data as UploadFile[])
watch(
  () => props.data,
  (newVal) => {
    data.value = newVal as UploadFile[]
  },
  {
    deep: true,
    immediate: true,
  },
)
const className = ref(props.class)
const drag = ref(!!props.drag)
const accept = ref(props.accept)
const multiple = ref(props.multiple)
const uploadRef = ref<any>(null)
const uploadLoading = ref(false)
function clearFiles() {
  uploadRef.value.clearFiles()
}

function handleExceed() {
  ElMessage({
    type: 'warning',
    message: '文件数量超过限制',
  })
}

const handlePreview: UploadProps['onPreview'] = (file) => {
  // 优化预览图片拿不到图片url
  const curFile = data.value.find(f => file.uid === f.uid)
  const url = file.url || curFile?.url
  file.url = url
  emit('preview', file)
}

const handleRemove: UploadProps['onRemove'] = (file) => {
  if (file.uid) {
    const idx = data.value.findIndex(item => item.uid === file.uid)
    if (idx > -1) {
      data.value.splice(idx, 1)
    }
    emit('remove', data.value)
  }
}
function handleBeforeUpload(file: UploadRawFile) {
  if (file?.name) {
    if (file.name.split('.')[0]?.length > 100) {
      ElMessage({
        type: 'warning',
        message: '文件名最长支持100个字符，请修改后重试!',
      })
      return false
    }
    const isLt10M = file.size / 1024 / 1024 < props.maxSize
    if (!isLt10M) {
      ElMessage({
        type: 'warning',
        message: `上传文件大小不能超过${props.maxSize}MB!`,
      })
      return false
    }
    if (props.onlyImg) {
      const isImg = ['jpg', 'png', 'jpeg', 'webp', 'gif'].includes(
        file.name.split('.')[file.name.split('.').length - 1],
      )
      if (!isImg) {
        ElMessage({
          type: 'warning',
          message: '只能上传图片',
        })
        return false
      }
    }
    const func = props.validate(file)
    if (func instanceof Promise) {
      return func
    }
    else {
      if (!func) {
        return false
      }
      emit('beforeUpload', file.name)
    }
  }
}

async function afterUpload(file: UploadRequestOptions) {
  const currentFile: UploadRawFile = file.file as UploadRawFile
  data.value.push({
    uid: currentFile.uid,
    name: currentFile.name,
    status: 'ready',
  })
  uploadLoading.value = true
  emit('start')
  const response = await upload(currentFile, props.url, props.customFileName)
  if (response) {
    data.value.filter((item: UploadFile) => {
      if (item.uid === currentFile.uid) {
        item.url = response.url
        item.response = response
        item.status = 'success'
      }
      return data.value
    })
  }
  else {
    const lists = data.value
    const idx = lists.findIndex(
      (item: UploadFile) => item.uid === currentFile.uid,
    )
    if (idx > -1) {
      data.value.splice(idx, 1)
      ElMessage.error('上传失败')
    }
  }
  uploadLoading.value = false
  emit('change', data.value)
}
defineExpose({
  clearFiles,
})
</script>

<template>
  <el-upload
    ref="uploadRef"
    v-loading="uploadLoading"
    :class="className"
    :with-credentials="true"
    :multiple="multiple"
    :drag="drag"
    :limit="limit"
    action="/upload"
    :on-preview="handlePreview"
    :on-remove="handleRemove"
    :on-exceed="handleExceed"
    :show-file-list="true"
    :http-request="afterUpload"
    :file-list="data"
    :accept="accept"
    :list-type="listType"
    :before-upload="handleBeforeUpload"
  >
    <slot />
    <template v-for="(_, slotName) in $slots" #[slotName]>
      <slot :name="slotName" />
    </template>
  </el-upload>
</template>
