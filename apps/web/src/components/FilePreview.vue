<script lang="ts" setup>
import { computed, ref, watch } from 'vue'

const props = defineProps({
  fileUrl: {
    type: String,
    required: true,
  },
  fileName: {
    type: String,
    required: true,
  },
  modelValue: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:modelValue'])

const visible = ref(props.modelValue)
watch(
  () => props.modelValue,
  (newVal) => {
    visible.value = newVal
  },
)
const isImage = computed(() => {
  const ext = props.fileName.split('.').pop()?.toLowerCase()
  return ['png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp'].includes(ext || '')
})

const isPdf = computed(() => {
  const ext = props.fileName.split('.').pop()?.toLowerCase()
  return ext === 'pdf'
})

function downloadFile() {
  const link = document.createElement('a')
  link.href = props.fileUrl
  link.download = props.fileName
  link.click()
}
</script>

<template>
  <el-dialog v-model="visible" append-to-body :title="fileName" @close="emit('update:modelValue')">
    <div v-if="isImage" class="flex justify-center">
      <el-image
        :src="fileUrl"
        alt="Preview"
        style="width: 80%;"
      />
    </div>
    <div v-else-if="isPdf">
      <iframe :src="fileUrl" alt="Preview" style="width: 100%; height: 80vh" />
    </div>
    <div v-else>
      <p>暂不支持预览该文件类型，请下载查看。</p>
      <el-button type="primary" @click="downloadFile">
        下载文件
      </el-button>
    </div>
  </el-dialog>
</template>
