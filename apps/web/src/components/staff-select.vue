<script lang="ts" setup>
import { debounce } from '@mis/shared'
import axios from 'axios'
import { ref, watch } from 'vue'

export interface StaffItem {
  nickname: string
  username: string
  departmentName: string
  id: number
}

interface ListItem {
  value: string
  label: string
}

const props = defineProps({
  style: { type: String, default: () => 'width: 100%' },
  modelValue: { type: [Array, Object, String], default: () => [] },
  placeholder: { type: String, default: () => '请输入' },
  multiple: { type: Boolean, default: () => true },
  relatedUserNames: { type: Array, required: false, default: () => [] },
  options: { type: Array, required: false, default: () => [] },
  disabled: {
    type: Boolean,
    default: () => false,
  },
  nickName: { type: String, default: '' },
  option: { type: Object, default: () => {} },
})

const emit = defineEmits(['update:modelValue', 'update:option'])
watch(() => props.nickName, (cur) => {
  if (cur) {
    query(cur)
  }
})
const val = ref(props.modelValue)
const multiple = ref(props.multiple)
const options = ref<ListItem[]>(props.options as ListItem[])

const loading = ref(false)

async function query(str: string) {
  if (!str) {
    return
  }
  try {
    loading.value = true
    const res = await axios.get(`/security-api/account/name?name=${str}`)
    if (res?.data) {
      options.value = (res?.data || []).map((item: StaffItem) => ({
        value: item.username,
        label: `${item.departmentName}-${item.nickname}`,
        id: item.id,
        name: item.username,
        nickname: item.nickname,
      }))
    }
    else {
      options.value = []
    }
  }
  finally {
    loading.value = false
  }
}
function batchQuery(qry: string): Promise<Array<any>> {
  // eslint-disable-next-line no-async-promise-executor
  return new Promise(async (resolve, reject) => {
    try {
      const res = await axios.get(`/security-api/account/name?name=${qry}`)
      if (res?.data) {
        resolve(
          (res?.data || []).map((item: StaffItem) => ({
            value: item.username,
            label: `${item.departmentName}-${item.nickname}`,
            id: item.id,
            name: item.username,
          })),
        )
      }
    }
    catch (err) {
      reject(err)
    }
  })
}

watch(
  () => props.modelValue,
  (value) => {
    val.value = value
    if (!value || (Array.isArray(value) && value.length === 0)) {
      val.value = []
    }
  },
)

watch(
  () => props.relatedUserNames,
  (cur) => {
    if (cur && cur.length) {
      Promise.all(cur.map(it => batchQuery(it as string))).then((res) => {
        if (res.length) {
          options.value = res.flat()
        }
      })
    }
  },
  {
    immediate: true,
  },
)
watch(
  () => props.options,
  (value) => {
    if (!value || (Array.isArray(value) && value.length === 0)) {
      options.value = []
    }
    else {
      options.value = value as ListItem[]
    }
  },
)

const remoteMethod = debounce(async (str: string) => {
  query(str)
}, 800)

function handleChange(value: Array<any>) {
  // const relatedUserIds = multiple.value ? value.map((it) => it.id) : value;
  emit('update:modelValue', value)
  emit('update:option', options.value.find(item => item.value === value))
}
</script>

<template>
  <el-select
    v-model="val"
    :multiple="multiple"
    filterable
    clearable
    remote
    :reserve-keyword="false"
    collapse-tags
    collapse-tags-tooltip
    :style="style"
    size="default"
    :placeholder="placeholder"
    :remote-method="remoteMethod"
    :loading="loading"
    :disabled="props.disabled"
    @change="handleChange"
  >
    <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
  </el-select>
</template>
