<script lang="ts" setup>
import { debounce } from '@mis/shared'
import axios from 'axios'
import { ref, watch } from 'vue'

export interface UnitItem {
  id: number
  name: string
  value: number
}

interface ListItem {
  value: string
  label: string
}

const props = defineProps({
  style: { type: String, default: () => 'width: 100%' },
  modelValue: { type: [Array, Object, String], default: () => [] },
  placeholder: { type: String, default: () => '请输入' },
  multiple: { type: Boolean, default: () => true },
  qryNames: { type: Array, required: false, default: () => [] },
  options: { type: Array, required: false, default: () => [] },
  disabled: {
    type: Boolean,
    default: () => false,
  },
  nickName: { type: String, default: '' },
  option: { type: Object, default: () => {} },
})

const emit = defineEmits(['update:modelValue', 'update:option'])

const url = ref<string>('/api-mis/dict/list')

watch(() => props.nickName, (cur) => {
  if (cur) {
    query(cur)
  }
})
const val = ref(props.modelValue)
const multiple = ref(props.multiple)
const options = ref<ListItem[]>(props.options as ListItem[])

const loading = ref(false)

async function query(str: string) {
  if (!str) {
    return
  }
  try {
    loading.value = true
    const res = await axios.get(`${url.value}?name=${(str && encodeURIComponent(str)) || ''}`)
    if (res?.data) {
      const { data = [] } = res.data
      options.value = data.map((item: UnitItem) => ({
        value: item.value,
        label: item.name,
        id: item.id,
      }))
    }
    else {
      options.value = []
    }
  }
  finally {
    loading.value = false
  }
}
function batchQuery(qry: string): Promise<Array<any>> {
// 移除 async 关键字，因为 Promise 执行器函数不应该是异步的
  return new Promise((resolve, reject) => {
    try {
      // 由于当前在 Promise 执行器函数中，不能使用 await，改为使用 .then 方法
      axios.get(`${url.value}?name=${(qry && encodeURIComponent(qry)) || ''}`).then((res) => {
        if (res?.data) {
          const { data = [] } = res.data
          resolve(
            data.map((item: UnitItem) => ({
              value: item.value,
              id: item.id,
              label: item.name,
            })),
          )
        }
      }).catch((err) => {
        reject(err)
      })
    }
    catch (err) {
      reject(err)
    }
  })
}

watch(
  () => props.modelValue,
  (value) => {
    val.value = value
    if (!value || (Array.isArray(value) && value.length === 0)) {
      val.value = []
    }
  },
)

watch(
  () => props.qryNames,
  (cur) => {
    if (cur && cur.length) {
      Promise.all(cur.map(it => batchQuery(it as string))).then((res) => {
        if (res.length) {
          options.value = res.flat()
        }
      })
    }
  },
  {
    immediate: true,
  },
)
watch(
  () => props.options,
  (value) => {
    if (!value || (Array.isArray(value) && value.length === 0)) {
      options.value = []
    }
    else {
      options.value = value as ListItem[]
    }
  },
)

const remoteMethod = debounce(async (str: string) => {
  query(str)
}, 800)

function handleChange(value: Array<any> | string) {
  // const relatedUserIds = multiple.value ? value.map((it) => it.id) : value;
  emit('update:modelValue', value)
  if (!multiple.value && value) {
    emit('update:option', options.value.find(item => item.value === value))
  }
}
</script>

<template>
  <el-select
    v-model="val"
    :multiple="multiple"
    filterable
    clearable
    remote
    :reserve-keyword="false"
    collapse-tags
    collapse-tags-tooltip
    :style="style"
    size="default"
    :placeholder="placeholder"
    :remote-method="remoteMethod"
    :loading="loading"
    :disabled="props.disabled"
    @change="handleChange"
  >
    <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
  </el-select>
</template>
