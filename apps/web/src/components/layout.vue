<script setup lang="ts">
import type { User } from '@/types'
import { inject, ref, watch } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const activeIndex = ref('/')
function handleSelect(key: string) {
  activeIndex.value = key
}

const user = inject<User | undefined>('user')
watch(
  () => router,
  () => {
    if (router?.currentRoute) {
      const curPath = router.currentRoute.value?.fullPath?.split('?')[0]
      if (curPath !== activeIndex.value) {
        activeIndex.value = curPath
      }
    }
  },
  {
    deep: true,
    immediate: true,
  },
)
</script>

<template>
  <div>
    <div class="flex justify-between mb-8px">
      <el-menu
        class="flex-1"
        style="background-color: #262a45"
        mode="horizontal"
        text-color="#999"
        active-text-color="#ffd04b"
        router
        :default-active="activeIndex"
        @select="handleSelect"
      >
        <el-sub-menu index="1">
          <template #title>
            OKR
          </template>
          <el-menu-item index="/admin/okr">
            OKR管理
          </el-menu-item>
          <el-menu-item index="/admin/okr-task">
            任务管理
          </el-menu-item>
        </el-sub-menu>
        <el-sub-menu index="2">
          <template #title>
            通用
          </template>
          <el-menu-item index="/admin/dict">
            枚举管理
          </el-menu-item>
        </el-sub-menu>
      </el-menu>
      <div class="color-#ddd h-59px bg-#262a45 lh-59px pr-12px">
        {{ user?.nickname }}
      </div>
    </div>
    <router-view />
  </div>
</template>

<style scoped>
.el-menu {
  background-color: #fff;
  border-bottom: 1px solid #e8e8e8;
}
</style>
