<script setup lang="ts">
import { reactive, ref, watch } from 'vue'

interface IPageInfo {
  pageNum: number
  pageSize: number
  total: number
}
export type IPageParams = Omit<IPageInfo, 'total'>
export interface SortType {
  field: string
  order: string | null
}
const props = defineProps({
  gridConfig: { type: Object, required: true, default: () => {} },
  gridEvents: { type: Object },
  pageInfo: { type: Object },
})

const emits = defineEmits<{
  (e: 'pageChange', params: IPageParams): void
  (e: 'sortChange', { field, order }: SortType): void
}>()

const pageInfo = reactive<IPageInfo>({
  pageNum: 1,
  pageSize: 10,
  total: 0,
})

const gridConfig = ref(props.gridConfig)
const gridRef = ref<any>(null)
function handlePageChange({ currentPage, pageSize }: { currentPage: number, pageSize: number }) {
  emits('pageChange', {
    pageNum: currentPage,
    pageSize,
  })
}
watch(
  [props.pageInfo],
  ([newPageInfo]) => {
    if (newPageInfo) {
      pageInfo.total = (newPageInfo as IPageInfo).total
      pageInfo.pageSize = (newPageInfo as IPageInfo).pageSize
      pageInfo.pageNum = (newPageInfo as IPageInfo).pageNum
    }
  },
  {
    deep: true,
    immediate: true,
    flush: 'sync',
  },
)
defineExpose({
  tableIns: gridRef,
})
const scrollX = {
  gt: 100,
}
const scrollY = {
  enabled: true,
  gt: 100,
}
function handleSortChange({ field, order }: SortType) {
  emits('sortChange', { field, order })
}
</script>

<template>
  <vxe-grid
    v-bind="gridConfig"
    ref="gridRef"
    :scroll-x="scrollX"
    :scroll-y="scrollY"
    class="mytable-style"
    header-cell-class-name="color-custom"
    v-on="props.gridEvents"
    @sort-change="handleSortChange"
  >
    <template v-for="(_, slotName) in $slots" #[slotName]="scope">
      <slot :name="slotName" v-bind="scope" />
    </template>
    <template #empty>
      <div class="h-300px">
        <el-empty>
          <template #description>
            暂无数据
          </template>
        </el-empty>
      </div>
    </template>
    <template v-if="props.pageInfo" #pager>
      <vxe-pager
        v-model:current-page="pageInfo.pageNum"
        v-model:page-size="pageInfo.pageSize"
        :layouts="[
          'Total',
          'Sizes',
          'PrevJump',
          'PrevPage',
          'Number',
          'NextPage',
          'NextJump',
          'FullJump',
          'PageCount',
        ]"
        :page-sizes="[10, 20, 50, 100, 200, 500]"
        :border="true"
        :total="pageInfo.total"
        class-name="VXE-pager"
        @page-change="handlePageChange"
      />
    </template>
  </vxe-grid>
</template>

<style scoped>
.color-custom {
  background-image: #EBF6FD !important;
}
</style>
