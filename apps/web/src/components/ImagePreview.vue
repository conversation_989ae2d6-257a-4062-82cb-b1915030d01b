<script setup lang="ts">
defineProps<{
  files: string[]
}>()
</script>

<template>
  <div v-if="files?.length" class="mt-10px">
    <el-image
      v-for="(file, index) in files"
      :key="index"
      :src="file"
      :preview-src-list="files"
      :initial-index="index"
      fit="cover"
      class="cursor-pointer"
      style="width: 100px; height: 100px; margin-right: 10px; margin-bottom: 10px;"
    />
  </div>
</template>
