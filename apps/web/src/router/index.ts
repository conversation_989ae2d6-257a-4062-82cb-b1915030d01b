import { createRouter, createWebHistory } from 'vue-router'
import baseLayout from '@/components/base-layout.vue'
import Layout from '@/components/layout.vue'
import OkrView from '../pages/okr/okr.vue'

const router = createRouter({
  history: createWebHistory('/mis/'),
  routes: [
    {
      path: '/',
      redirect: '/admin/okr',
    },
    {
      path: '/admin',
      name: 'admin',
      component: Layout,
      children: [
        {
          path: 'okr',
          name: 'okrHome',
          component: OkrView,
        },
        {
          path: 'okr-task',
          name: 'okrTask',
          component: () => import('../pages/okr/task.vue'),
        },
        {
          path: 'dict',
          name: 'dict',
          component: () => import('../pages/dict/index.vue'),
        },
      ],
    },
    {
      path: '/okr',
      name: 'okr',
      component: baseLayout,
      children: [
        {
          path: 'weekly/:id',
          name: 'weeklyReport',
          component: () => import('../pages/okr/weekly/index.vue'),
        },
        {
          path: 'overview/:id',
          name: 'reportOverview',
          component: () => import('../pages/okr/overview/index.vue'),
        },
      ],
    },
  ],
})
export default router
