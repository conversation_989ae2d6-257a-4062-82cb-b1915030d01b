import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import ElementPlus from 'element-plus'
import zhCn from 'element-plus/es/locale/lang/zh-cn'

import { createApp } from 'vue'
import VxeUIAll from 'vxe-pc-ui'
import VXETable from 'vxe-table'
import App from './App.vue'
import router from './router'
import './assets/base.css'
import 'uno.css'
import 'vxe-pc-ui/lib/style.css'
import 'vxe-table/lib/style.css'
import 'element-plus/dist/index.css'

function bootstrap() {
  const app = createApp(App)
  for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component)
  }
  app.use(router)
  app.use(VxeUIAll).use(VXETable)
  app.use(ElementPlus, {
    locale: zhCn,
  })
  app.mount('#app')
}
bootstrap()
