import type { Ref } from 'vue'
import type { User } from '@/types'
import api from '@mis/shared/src/request'
import { onMounted, ref } from 'vue'

export type InjectUser = Ref<User | undefined>
async function getUser() {
  const [res, _] = await Promise.all([api({
    url: '/api-mis/user',
    method: 'get',
  }), api({
    url: '/internal-api/user',
    method: 'get',
  })])
  if (res && res.data.username && res.data.nickname) {
    return res.data as User
  }
  return undefined
}
export function useUser() {
  const user: InjectUser = ref(undefined)
  onMounted(async () => {
    user.value = await getUser()
  })
  return user
}
