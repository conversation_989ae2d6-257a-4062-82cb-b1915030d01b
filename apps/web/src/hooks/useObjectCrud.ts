import type { VxeGridListeners, VxeGridProps } from 'vxe-table'
import api from '@mis/shared/src/request'
import { ElMessage, ElMessageBox, ElNotification } from 'element-plus'
import { reactive, ref } from 'vue'
import {
  columnFields,
} from '../constants/index'

export default function useListCrud(urlConfig: Record<string, any>) {
  const formRef = ref()
  const defaultPageInfo = {
    pageNum: 1,
    pageSize: 10,
  } as const
  const pageInfo = reactive({
    ...defaultPageInfo,
    total: 0,
  })
  const gridProps = reactive<VxeGridProps>({
    border: true,
    loading: false,
    columns: columnFields,
    maxHeight: 640,
    data: [],
    toolbarConfig: {
      tools: [{ name: '创建', status: 'primary', code: 'add' }],
    },
    columnConfig: {
      resizable: true,
    },
    showOverflow: true,
  })
  // 右侧回调事件
  const executeTool = (code: string) => {
    switch (code) {
      case 'add':
        break
      default:
        break
    }
  }
  const gridEvents: VxeGridListeners = {
    toolbarToolClick({ code }) {
      executeTool(code)
    },
  }
  const getParams = (form: Record<string, any>) => {
    const formFields = {
      ...form,
    }
    return {
      ...formFields,
      pageNum: pageInfo.pageNum,
      pageSize: pageInfo.pageSize,
    }
  }
  // 列表查询
  const getPoolList = async (form: Record<string, any>) => {
    const data = getParams(form)
    try {
      gridProps.loading = true
      const res = await api({
        url: urlConfig.list,
        method: 'get',
        data,
      })
      if (res?.data) {
        pageInfo.total = res.totalElements || res.data.length
        gridProps.data = res.data || []
      }
    }
    catch (err) {
      console.error(err)
    }
    finally {
      gridProps.loading = false
    }
  }

  const form = ref<any>({})
  // 导出
  const exportLoading = ref<boolean>(false)
  const handleExport = async (form: Record<string, any>) => {
    exportLoading.value = true
    const data = getParams(form)
    const res = await api({
      url: urlConfig.export,
      data,
      method: 'post',
    })
    exportLoading.value = false
    if (res?.success) {
      ElNotification({
        type: 'success',
        title: '提示',
        message: res.msg || res.data || '导出成功',
      })
    }
  }
  // 删除
  const handleDeleteRequest = async (ids: number[]) => {
    const url = urlConfig.delete
    const res = await api({ url, method: 'put', data: ids })
    if (res?.success) {
      ElMessage({
        type: 'success',
        message: `删除成功！`,
      })
      getPoolList(formRef.value?.formFieldData)
      return true
    }
    else {
      return false
    }
  }
  const handleDelete = (ids: number[]) => {
    return new Promise<boolean>((resolve) => {
      ElMessageBox.confirm(`确定删除吗？`, '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          resolve(await handleDeleteRequest(ids))
        })
        .catch(() => {
          resolve(false)
        })
    })
  }
  return {
    gridProps,
    formRef,
    pageInfo,
    defaultPageInfo,
    getPoolList,
    gridEvents,
    form,
    handleExport,
    exportLoading,
    handleDelete,
  }
}
