INSERT INTO `fe_mis_objective` (`id`,`gmt_create`,`gmt_modified`,`tier1`,`tier2`,`name`,`action`,`unit`,`is_deleted`,`creator`,`updater`, `year`) VALUES (4,'2025-05-08 15:17:22','2025-05-21 13:25:45','商品竞争力','AI商品数据工厂','商品数据累计获取品牌数','通过RPA到品牌原网站或三方平台，获取品牌的商品数据。',1,0,'jia.xin','jia.xin',2025);
INSERT INTO `fe_mis_objective` (`id`,`gmt_create`,`gmt_modified`,`tier1`,`tier2`,`name`,`action`,`unit`,`is_deleted`,`creator`,`updater`, `year`) VALUES (5,'2025-05-08 16:15:25','2025-05-21 13:25:45','商品竞争力','AI商品数据工厂','重点类目商品数据竞争力累计提升数','人工优化提升现有商品数据质量，包括目录治理、属性治理、图片优化。',1,0,'jia.xin','jia.xin',2025);
INSERT INTO `fe_mis_objective` (`id`,`gmt_create`,`gmt_modified`,`tier1`,`tier2`,`name`,`action`,`unit`,`is_deleted`,`creator`,`updater`, `year`) VALUES (6,'2025-05-08 16:25:44','2025-05-21 13:25:45','商品竞争力','AI商品力大脑','AI商品力大脑优化现有目标品类竞争力累计带来GMV增量','1.通过诊断售前、售中、售后对应的商品力指标，给出商品竞争力的诊断结果与改善建议，推动产线跟进解决。
2.25年6月完成两条产线的试点论证，25年7月完成商品力策略平台上线，25年下半年逐步扩充至全部产线。',4,0,'jia.xin','jia.xin',2025);
INSERT INTO `fe_mis_objective` (`id`,`gmt_create`,`gmt_modified`,`tier1`,`tier2`,`name`,`action`,`unit`,`is_deleted`,`creator`,`updater`, `year`) VALUES (7,'2025-05-08 16:36:17','2025-05-21 13:25:45','市场占有率','客户商品池运营','客户商品池运营带来目标客户GMV增量','1.运营侧：推动商品池健康度策略线上化、人工执行商品池健康度调优、推动手工环节Agent提效
2.产品侧：优化推品策略能力以提升推品准确率及采纳率、升级SEO标题改写模型及策略、完善客户商品池管理和运营的基础功能',4,0,'jia.xin','jia.xin',2025);
INSERT INTO `fe_mis_objective` (`id`,`gmt_create`,`gmt_modified`,`tier1`,`tier2`,`name`,`action`,`unit`,`is_deleted`,`creator`,`updater`, `year`) VALUES (8,'2025-05-08 16:40:17','2025-05-21 13:25:45','市场占有率','AI询报价','【定价大脑】TOP2000品牌高频询价商品毛利额提升','1.推动产线侧会员价统一管理
2.搭建客制化定价模型',4,0,'jia.xin','jia.xin',2025);
INSERT INTO `fe_mis_objective` (`id`,`gmt_create`,`gmt_modified`,`tier1`,`tier2`,`name`,`action`,`unit`,`is_deleted`,`creator`,`updater`, `year`) VALUES (15,'2025-05-09 16:22:24','2025-05-21 13:25:45','市场占有率','AI询报价','智能体报出询价行人工采纳率','1.自动定品Agent覆盖全部产线
2.召回能力、商品数据、需求澄清及需求预处理等环节优化，以提升定品准确率',5,0,'jia.xin','jia.xin',2025);
INSERT INTO `fe_mis_objective` (`id`,`gmt_create`,`gmt_modified`,`tier1`,`tier2`,`name`,`action`,`unit`,`is_deleted`,`creator`,`updater`, `year`) VALUES (16,'2025-05-09 16:24:50','2025-05-21 13:25:45','市场占有率','官网','搜索点击率','1.运营侧：搜索模型的调优，搜索运营位的升级、搜索交互改善需求
2.技术侧：搜索全链路模型升级，包括查询理解、召回、粗排/精排/重排',5,0,'jia.xin','jia.xin',2025);
INSERT INTO `fe_mis_objective` (`id`,`gmt_create`,`gmt_modified`,`tier1`,`tier2`,`name`,`action`,`unit`,`is_deleted`,`creator`,`updater`, `year`) VALUES (17,'2025-05-09 16:31:05','2025-05-21 13:25:45','市场占有率','AI搜索','一次会话点击率','通过AI行家助手引导用户进行需求澄清、定品推优给出唯一SKU，给出准确率高的结果，降低用户的选择难度，提升点击率',5,0,'jia.xin','jia.xin',2025);
INSERT INTO `fe_mis_objective` (`id`,`gmt_create`,`gmt_modified`,`tier1`,`tier2`,`name`,`action`,`unit`,`is_deleted`,`creator`,`updater`, `year`) VALUES (18,'2025-05-09 16:33:35','2025-05-21 13:25:45','市场占有率','官网','官网GMV累计金额','1.官网高流量商品竞争力PACE治理
2.官网会员价体系改革
3.搜索优化
4.分客群营销',4,0,'jia.xin','jia.xin',2025);
INSERT INTO `fe_mis_objective` (`id`,`gmt_create`,`gmt_modified`,`tier1`,`tier2`,`name`,`action`,`unit`,`is_deleted`,`creator`,`updater`, `year`) VALUES (19,'2025-05-09 16:34:18','2025-05-21 13:25:45','市场占有率','官网','官网DAU','1. AI工具（AI搜索、AI行家助手、AI采购助手、AI物料管家等）提升用户使用官网频次；
2. 需求员&采购员使用协同清单工具，提升需求员官网使用频次。数据现状：官网整体DAU中，需求员：采购员=1:9；高频使用协同清单工具的客户（集团），需求员：采购员=1:1',1,0,'jia.xin','jia.xin',2025);
INSERT INTO `fe_mis_objective` (`id`,`gmt_create`,`gmt_modified`,`tier1`,`tier2`,`name`,`action`,`unit`,`is_deleted`,`creator`,`updater`, `year`) VALUES (20,'2025-05-09 16:35:01','2025-05-21 13:25:45','市场占有率','CRM (AI销售助手)','销售单次拜访价值效率提升（对照组使用CRM对比不使用CRM）','1.运营侧：搭建客户优先级等模型、推动核心功能上线及业务测试
2. 产品侧：自研AI对话式APP，提升用户使用AI销售助手的场景频次；同时结合策略模型，给销售推送拜访、商机、情报采集任务。',5,0,'jia.xin','jia.xin',2025);
INSERT INTO `fe_mis_objective` (`id`,`gmt_create`,`gmt_modified`,`tier1`,`tier2`,`name`,`action`,`unit`,`is_deleted`,`creator`,`updater`, `year`) VALUES (21,'2025-05-09 16:35:58','2025-05-21 13:25:45','运营效率','智能工作台','Agent场景数','洞察客服、采购等部门的重复性工作场景，挖掘可Agent化机会，按场景逐个搭建Agent。',1,0,'jia.xin','jia.xin',2025);
INSERT INTO `fe_mis_objective` (`id`,`gmt_create`,`gmt_modified`,`tier1`,`tier2`,`name`,`action`,`unit`,`is_deleted`,`creator`,`updater`, `year`) VALUES (22,'2025-05-09 16:37:49','2025-05-21 13:25:45','运营效率','AI行家助手','减少人工回复次数','1.AI行家助手进官网承接AI搜索、商详页问询
2.自动监听、回答群内客户问题
3.扩充服务场景，如企业知识库、商品推荐、商品价格变动推送销售、非VC商家消息触达。',5,0,'jia.xin','jia.xin',2025);
INSERT INTO `fe_mis_objective` (`id`,`gmt_create`,`gmt_modified`,`tier1`,`tier2`,`name`,`action`,`unit`,`is_deleted`,`creator`,`updater`, `year`) VALUES (23,'2025-05-09 16:38:30','2025-05-21 13:25:45','市场占有率','AI产业化','AI应用注册客户数','由“面”到“线”再到“点”的策略推进
1. “面”：25年4月份覆盖线上、线下渠道，包括但不限于站内，站外广告；达人，数字人直播等；5月,典型客户案例制作和推广，AI物料管家关注中信易家&中信重工，AI行家助手关注河北港口；6月,推出3个行业AI应用解决方案
2.“线”：与经信委、国资委、数字协会等组织合作
3.“点”：与云学院合作推进销售全员培训与重点客户协同跟进',1,0,'jia.xin','jia.xin',2025);
INSERT INTO `fe_mis_objective` (`id`,`gmt_create`,`gmt_modified`,`tier1`,`tier2`,`name`,`action`,`unit`,`is_deleted`,`creator`,`updater`, `year`) VALUES (24,'2025-05-09 16:39:33','2025-05-21 13:25:45','市场占有率','AI产业化','AI应用商业化累计营收','由“面”到“线”再到“点”的策略推进
1. “面”：25年4月份覆盖线上、线下渠道，包括但不限于站内，站外广告；达人，数字人直播等；5月,典型客户案例制作和推广，AI物料管家关注中信易家&中信重工，AI行家助手关注河北港口；6月,推出3个行业AI应用解决方案
2.“线”：与经信委、国资委、数字协会等组织合作
3.“点”：与云学院合作推进销售全员培训与重点客户协同跟进',4,0,'jia.xin','jia.xin',2025);
INSERT INTO `fe_mis_objective` (`id`,`gmt_create`,`gmt_modified`,`tier1`,`tier2`,`name`,`action`,`unit`,`is_deleted`,`creator`,`updater`, `year`) VALUES (25,'2025-05-09 16:40:49','2025-05-21 13:25:45','基础建设','数据字典','数据字典基础建设完成率','数据源梳理与整合，数据血缘关系梳理、数据目录管理、自主SQL查询功能开发、数据资产监控与可视化、数据质量管理、数据安全保障',5,0,'jia.xin','jia.xin',2025);
INSERT INTO `fe_mis_objective` (`id`,`gmt_create`,`gmt_modified`,`tier1`,`tier2`,`name`,`action`,`unit`,`is_deleted`,`creator`,`updater`, `year`) VALUES (26,'2025-05-09 16:41:27','2025-05-21 13:25:45','基础建设','运维','AI代码生成率','1、目标拆解、AI编码工具全域使用、基础使用指导
2、流程嵌入 + 进阶培训 + 标杆团队打造
3、数据驱动优化 + 跨团队经验复制 + 创新激励
4、全链路闭环 + 最佳实践固化 + 下阶段规划',5,0,'jia.xin','jia.xin',2025);
INSERT INTO `fe_mis_objective` (`id`,`gmt_create`,`gmt_modified`,`tier1`,`tier2`,`name`,`action`,`unit`,`is_deleted`,`creator`,`updater`, `year`) VALUES (27,'2025-05-09 16:42:02','2025-05-21 13:25:45','基础建设','安全','安全运行率','1、规划1-3年安全成熟度目标，在安全合规、安全技术、安全管理上规划安全项目，通过安全综合覆盖度评估安全项目完成进度；
2、周期性进行安全风险评估，使用风险暴露指数跟踪一象限安全风险。',5,0,'jia.xin','jia.xin',2025);
INSERT INTO `fe_mis_objective` (`id`,`gmt_create`,`gmt_modified`,`tier1`,`tier2`,`name`,`action`,`unit`,`is_deleted`,`creator`,`updater`, `year`) VALUES (28,'2025-05-09 16:42:30','2025-05-21 13:25:45','基础建设','AI开发者平台','AI开发者平台完成率','从AI模型的广场建设，再到模型的选择接入，再到Agent平台的管理，及模型应用的成本、性能监控，实现一站式的AI应用开发平台',5,0,'jia.xin','jia.xin',2025);
INSERT INTO `fe_mis_objective` (`id`,`gmt_create`,`gmt_modified`,`tier1`,`tier2`,`name`,`action`,`unit`,`is_deleted`,`creator`,`updater`, `year`) VALUES (29,'2025-05-09 16:43:44','2025-05-21 13:25:45','基础建设','AI测试','AI测试活动占比','系统性引入并规模化应用AI测试能力，推动测试流程从“人力型测试”向“智能型测试”转变。通过AI参与测试用例生成、自动化，联调活动等关键环节，实现测试活动中30%以上的产出由AI能力提供，显著提升测试效率与覆盖深度，保障业务快速迭代过程中的交付质量与稳定性。',5,0,'jia.xin','jia.xin',2025);
INSERT INTO `fe_mis_objective` (`id`,`gmt_create`,`gmt_modified`,`tier1`,`tier2`,`name`,`action`,`unit`,`is_deleted`,`creator`,`updater`, `year`) VALUES (30,'2025-05-09 16:44:35','2025-05-21 13:25:45','海外','海外','海外（OKR暂未录入）','暂无',1,0,'jia.xin','jia.xin',2025);
INSERT INTO `fe_mis_objective` (`id`,`gmt_create`,`gmt_modified`,`tier1`,`tier2`,`name`,`action`,`unit`,`is_deleted`,`creator`,`updater`, `year`) VALUES (31,'2025-05-09 16:44:50','2025-05-21 13:25:45','坤同','坤同','坤同（OKR暂未录入）','暂无',1,0,'jia.xin','jia.xin',2025);