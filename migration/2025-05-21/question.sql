INSERT INTO `fe_mis_objective_question` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`question`,`is_deleted`,`creator`,`updater`, `task_id`) VALUES (9,'2025-05-08 15:18:07','2025-05-09 16:11:36',4,'过去两周，商品数据的增长带来哪些业务场景结果的量化变化？',0, 'jia.xin','jia.xin', NULL);
INSERT INTO `fe_mis_objective_question` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`question`,`is_deleted`,`creator`,`updater`, `task_id`) VALUES (10,'2025-05-08 15:18:21','2025-05-09 16:11:42',4,'在过去两周遇到哪些问题和挑战，以及接下来两周的工作计划。',0, 'jia.xin','jia.xin', NULL);
INSERT INTO `fe_mis_objective_question` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`question`,`is_deleted`,`creator`,`updater`, `task_id`) VALUES (11,'2025-05-08 16:17:53','2025-05-09 16:14:07',5,'在过去两周遇到哪些问题和挑战，以及接下来两周的工作计划。',0, 'jia.xin','jia.xin', NULL);
INSERT INTO `fe_mis_objective_question` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`question`,`is_deleted`,`creator`,`updater`, `task_id`) VALUES (12,'2025-05-08 16:27:38','2025-05-12 18:58:27',6,'在过去两周，AI商品力大脑沉淀了哪些核心数据？',0, 'jia.xin','jia.xin', NULL);
INSERT INTO `fe_mis_objective_question` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`question`,`is_deleted`,`creator`,`updater`, `task_id`) VALUES (13,'2025-05-08 16:27:47','2025-05-12 18:58:36',6,'请量化过去两周AI商品力大脑的运算次数、输出策略次数、被采纳次数。',0, 'jia.xin','jia.xin', NULL);
INSERT INTO `fe_mis_objective_question` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`question`,`is_deleted`,`creator`,`updater`, `task_id`) VALUES (14,'2025-05-08 16:27:57','2025-05-12 18:58:42',6,'在过去两周，AI商品力大脑进行中的实验的进展如何，以及接下来两周的工作计划',0, 'jia.xin','jia.xin', NULL);
INSERT INTO `fe_mis_objective_question` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`question`,`is_deleted`,`creator`,`updater`, `task_id`) VALUES (15,'2025-05-08 16:38:06','2025-05-09 16:17:47',7,'在过去两周，AI推品与SEO大脑沉淀了哪些核心数据？',0, 'jia.xin','jia.xin', NULL);
INSERT INTO `fe_mis_objective_question` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`question`,`is_deleted`,`creator`,`updater`, `task_id`) VALUES (16,'2025-05-08 16:38:17','2025-05-09 16:17:54',7,'请量化过去两周AI推品大脑与SEO大脑的运算次数、输出策略次数、被采纳次数。',0, 'jia.xin','jia.xin', NULL);
INSERT INTO `fe_mis_objective_question` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`question`,`is_deleted`,`creator`,`updater`, `task_id`) VALUES (17,'2025-05-08 16:41:19','2025-05-09 16:20:39',8,'在过去两周，定品能力的提升带来哪些业务场景结果的量化变化？',0, 'jia.xin','jia.xin', NULL);
INSERT INTO `fe_mis_objective_question` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`question`,`is_deleted`,`creator`,`updater`, `task_id`) VALUES (18,'2025-05-09 16:17:58','2025-05-09 16:17:58',7,'在过去两周遇到哪些问题和挑战，以及接下来两周的工作计划。',0, 'jia.xin','jia.xin', NULL);
INSERT INTO `fe_mis_objective_question` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`question`,`is_deleted`,`creator`,`updater`, `task_id`) VALUES (19,'2025-05-09 16:20:44','2025-05-09 16:20:44',8,'在过去两周遇到哪些问题和挑战，以及接下来两周的工作计划。',0, 'jia.xin','jia.xin', NULL);
INSERT INTO `fe_mis_objective_question` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`question`,`is_deleted`,`creator`,`updater`, `task_id`) VALUES (20,'2025-05-09 16:22:46','2025-05-09 16:22:46',15,'在过去两周，AI询报价进行中的实验的进展如何，请量化实验数据前后对比效果。',0, 'jia.xin','jia.xin', NULL);
INSERT INTO `fe_mis_objective_question` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`question`,`is_deleted`,`creator`,`updater`, `task_id`) VALUES (21,'2025-05-09 16:22:50','2025-05-09 16:22:50',15,'过在过去两周遇到哪些问题和挑战，以及接下来两周的工作计划。',0, 'jia.xin','jia.xin', NULL);
INSERT INTO `fe_mis_objective_question` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`question`,`is_deleted`,`creator`,`updater`, `task_id`) VALUES (22,'2025-05-09 16:30:28','2025-05-09 16:30:28',16,'在过去两周，搜索点击率变化的核心动因是什么？是否总结出可复用的​​最佳实践​​？',0, 'jia.xin','jia.xin', NULL);
INSERT INTO `fe_mis_objective_question` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`question`,`is_deleted`,`creator`,`updater`, `task_id`) VALUES (23,'2025-05-09 16:30:34','2025-05-09 16:30:34',16,'在过去两周遇到哪些问题和挑战，以及接下来两周的工作计划。',0, 'jia.xin','jia.xin', NULL);
INSERT INTO `fe_mis_objective_question` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`question`,`is_deleted`,`creator`,`updater`, `task_id`) VALUES (24,'2025-05-09 16:31:16','2025-05-09 16:31:16',17,'在过去两周，AI搜索建设的进展如何？能力有哪些提升？',0, 'jia.xin','jia.xin', NULL);
INSERT INTO `fe_mis_objective_question` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`question`,`is_deleted`,`creator`,`updater`, `task_id`) VALUES (25,'2025-05-09 16:31:21','2025-05-09 16:31:21',17,'过去两周遇到哪些问题和挑战，以及接下来两周的工作计划。',0, 'jia.xin','jia.xin', NULL);
INSERT INTO `fe_mis_objective_question` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`question`,`is_deleted`,`creator`,`updater`, `task_id`) VALUES (26,'2025-05-09 16:33:43','2025-05-09 16:33:43',18,'在过去两周，官网GMV变化的核心动因是什么？是否总结出可复用的​​最佳实践​​？',0, 'jia.xin','jia.xin', NULL);
INSERT INTO `fe_mis_objective_question` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`question`,`is_deleted`,`creator`,`updater`, `task_id`) VALUES (28,'2025-05-09 16:34:32','2025-05-09 16:34:32',19,'在过去两周，官网DAU变化的核心动因是什么？是否总结出可复用的​​最佳实践​​？',0, 'jia.xin','jia.xin', NULL);
INSERT INTO `fe_mis_objective_question` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`question`,`is_deleted`,`creator`,`updater`, `task_id`) VALUES (29,'2025-05-09 16:34:37','2025-05-09 16:34:37',19,'过去两周遇到哪些问题和挑战，以及接下来两周的工作计划。',0, 'jia.xin','jia.xin', NULL);
INSERT INTO `fe_mis_objective_question` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`question`,`is_deleted`,`creator`,`updater`, `task_id`) VALUES (30,'2025-05-09 16:36:15','2025-05-09 16:36:15',20,'在过去两周，销售使用CRM带来哪些业务结果的量化变化？',0, 'jia.xin','jia.xin', NULL);
INSERT INTO `fe_mis_objective_question` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`question`,`is_deleted`,`creator`,`updater`, `task_id`) VALUES (31,'2025-05-09 16:36:19','2025-05-09 16:36:19',20,'过去两周遇到哪些问题和挑战，以及接下来两周的工作计划。',0, 'jia.xin','jia.xin', NULL);
INSERT INTO `fe_mis_objective_question` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`question`,`is_deleted`,`creator`,`updater`, `task_id`) VALUES (32,'2025-05-09 16:37:19','2025-05-09 16:37:19',21,'请量化过去两周智能工作台的用户数、对话数、处理订单数等数据，以衡量场景渗透率。',0, 'jia.xin','jia.xin', NULL);
INSERT INTO `fe_mis_objective_question` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`question`,`is_deleted`,`creator`,`updater`, `task_id`) VALUES (33,'2025-05-09 16:37:23','2025-05-09 16:37:23',21,'过去两周遇到哪些问题和挑战，以及接下来两周的工作计划。',0, 'jia.xin','jia.xin', NULL);
INSERT INTO `fe_mis_objective_question` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`question`,`is_deleted`,`creator`,`updater`, `task_id`) VALUES (34,'2025-05-09 16:37:57','2025-05-09 16:37:57',22,'请量化过去两周AI行家助手新进客户/供应商群数、累计进群数、以及自动回答客户问题的占比。',0, 'jia.xin','jia.xin', NULL);
INSERT INTO `fe_mis_objective_question` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`question`,`is_deleted`,`creator`,`updater`, `task_id`) VALUES (35,'2025-05-09 16:38:02','2025-05-09 16:38:02',22,'在过去两周遇到哪些问题和挑战，以及接下来两周的工作计划。',0, 'jia.xin','jia.xin', NULL);
INSERT INTO `fe_mis_objective_question` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`question`,`is_deleted`,`creator`,`updater`, `task_id`) VALUES (36,'2025-05-09 16:39:09','2025-05-09 16:39:09',23,'在过去两周，AI应用注册客户数增长的动因是什么？',0, 'jia.xin','jia.xin', NULL);
INSERT INTO `fe_mis_objective_question` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`question`,`is_deleted`,`creator`,`updater`, `task_id`) VALUES (37,'2025-05-09 16:39:43','2025-05-09 16:39:43',24,'在过去两周，AI应用订单金额停滞或增长的核心原因是什么？',0, 'jia.xin','jia.xin', NULL);
INSERT INTO `fe_mis_objective_question` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`question`,`is_deleted`,`creator`,`updater`, `task_id`) VALUES (38,'2025-05-09 16:39:48','2025-05-09 16:39:48',24,'在过去两周遇到哪些问题和挑战，以及接下来两周的工作计划。',0, 'jia.xin','jia.xin', NULL);
INSERT INTO `fe_mis_objective_question` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`question`,`is_deleted`,`creator`,`updater`, `task_id`) VALUES (39,'2025-05-09 16:40:58','2025-05-09 16:40:58',25,'在过去两周，数据字典的进展如何？能力有哪些提升？',0, 'jia.xin','jia.xin', NULL);
INSERT INTO `fe_mis_objective_question` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`question`,`is_deleted`,`creator`,`updater`, `task_id`) VALUES (40,'2025-05-09 16:41:02','2025-05-09 16:41:02',25,'在过去两周遇到哪些问题和挑战，以及接下来两周的工作计划。',0, 'jia.xin','jia.xin', NULL);
INSERT INTO `fe_mis_objective_question` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`question`,`is_deleted`,`creator`,`updater`, `task_id`) VALUES (41,'2025-05-09 16:41:36','2025-05-09 16:41:36',26,'在过去两周，AI代码的进展如何？能力有哪些提升？',0, 'jia.xin','jia.xin', NULL);
INSERT INTO `fe_mis_objective_question` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`question`,`is_deleted`,`creator`,`updater`, `task_id`) VALUES (42,'2025-05-09 16:41:40','2025-05-09 16:41:40',26,'在过去两周遇到哪些问题和挑战，以及接下来两周的工作计划。',0, 'jia.xin','jia.xin', NULL);
INSERT INTO `fe_mis_objective_question` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`question`,`is_deleted`,`creator`,`updater`, `task_id`) VALUES (43,'2025-05-09 16:42:13','2025-05-09 16:42:13',27,'在过去两周，如果有安全事故发生，请描述事故情况、解决方案及当前状态。',0, 'jia.xin','jia.xin', NULL);
INSERT INTO `fe_mis_objective_question` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`question`,`is_deleted`,`creator`,`updater`, `task_id`) VALUES (44,'2025-05-09 16:42:40','2025-05-09 16:42:40',28,'在过去两周，AI开发者平台建设的进展如何？能力有哪些提升？',0, 'jia.xin','jia.xin', NULL);
INSERT INTO `fe_mis_objective_question` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`question`,`is_deleted`,`creator`,`updater`, `task_id`) VALUES (45,'2025-05-09 16:42:45','2025-05-09 16:42:45',28,'在过去两周遇到哪些问题和挑战，以及接下来两周的工作计划。',0, 'jia.xin','jia.xin', NULL);
INSERT INTO `fe_mis_objective_question` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`question`,`is_deleted`,`creator`,`updater`, `task_id`) VALUES (46,'2025-05-09 16:43:55','2025-05-09 16:43:55',29,'在过去两周，AI测试的进展如何？能力有哪些提升？',0, 'jia.xin','jia.xin', NULL);
INSERT INTO `fe_mis_objective_question` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`question`,`is_deleted`,`creator`,`updater`, `task_id`) VALUES (47,'2025-05-09 16:44:01','2025-05-09 16:44:01',29,'过去两周遇到哪些问题和挑战，以及接下来两周的工作计划。',0, 'jia.xin','jia.xin', NULL);
INSERT INTO `fe_mis_objective_question` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`question`,`is_deleted`,`creator`,`updater`, `task_id`) VALUES (48,'2025-05-09 16:44:59','2025-05-09 16:44:59',30,'在过去两周遇到哪些问题和挑战，以及接下来两周的工作计划。',0, 'jia.xin','jia.xin', NULL);
INSERT INTO `fe_mis_objective_question` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`question`,`is_deleted`,`creator`,`updater`, `task_id`) VALUES (49,'2025-05-09 16:45:04','2025-05-09 16:45:04',31,'在过去两周遇到哪些问题和挑战，以及接下来两周的工作计划。',0, 'jia.xin','jia.xin', NULL);
INSERT INTO `fe_mis_objective_question` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`question`,`is_deleted`,`creator`,`updater`, `task_id`) VALUES (57,'2025-05-09 18:09:24','2025-05-12 19:00:01',18,'在过去两周遇到哪些问题和挑战，以及接下来两周的工作计划。',0, 'jia.xin','jia.xin', NULL);
INSERT INTO `fe_mis_objective_question` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`question`,`is_deleted`,`creator`,`updater`, `task_id`) VALUES (142,'2025-05-21 11:36:19','2025-05-21 11:36:19',4,'过去两周，商品数据的增长带来哪些业务场景结果的量化变化？',0, 'jia.xin','jia.xin', 1);
INSERT INTO `fe_mis_objective_question` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`question`,`is_deleted`,`creator`,`updater`, `task_id`) VALUES (143,'2025-05-21 11:36:20','2025-05-21 11:36:20',4,'在过去两周遇到哪些问题和挑战，以及接下来两周的工作计划。',0, 'jia.xin','jia.xin', 1);
INSERT INTO `fe_mis_objective_question` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`question`,`is_deleted`,`creator`,`updater`, `task_id`) VALUES (144,'2025-05-21 11:36:20','2025-05-21 11:36:20',5,'在过去两周遇到哪些问题和挑战，以及接下来两周的工作计划。',0, 'jia.xin','jia.xin', 1);
INSERT INTO `fe_mis_objective_question` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`question`,`is_deleted`,`creator`,`updater`, `task_id`) VALUES (145,'2025-05-21 11:36:21','2025-05-21 11:36:21',6,'在过去两周，AI商品力大脑沉淀了哪些核心数据？',0, 'jia.xin','jia.xin', 1);
INSERT INTO `fe_mis_objective_question` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`question`,`is_deleted`,`creator`,`updater`, `task_id`) VALUES (146,'2025-05-21 11:36:21','2025-05-21 11:36:21',6,'请量化过去两周AI商品力大脑的运算次数、输出策略次数、被采纳次数。',0, 'jia.xin','jia.xin', 1);
INSERT INTO `fe_mis_objective_question` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`question`,`is_deleted`,`creator`,`updater`, `task_id`) VALUES (147,'2025-05-21 11:36:21','2025-05-21 11:36:21',6,'在过去两周，AI商品力大脑进行中的实验的进展如何，以及接下来两周的工作计划',0, 'jia.xin','jia.xin', 1);
INSERT INTO `fe_mis_objective_question` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`question`,`is_deleted`,`creator`,`updater`, `task_id`) VALUES (148,'2025-05-21 11:36:22','2025-05-21 11:36:22',7,'在过去两周，AI推品与SEO大脑沉淀了哪些核心数据？',0, 'jia.xin','jia.xin', 1);
INSERT INTO `fe_mis_objective_question` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`question`,`is_deleted`,`creator`,`updater`, `task_id`) VALUES (149,'2025-05-21 11:36:22','2025-05-21 11:36:22',7,'请量化过去两周AI推品大脑与SEO大脑的运算次数、输出策略次数、被采纳次数。',0, 'jia.xin','jia.xin', 1);