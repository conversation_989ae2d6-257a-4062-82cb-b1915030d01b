INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (190,'2025-05-09 22:05:52','2025-05-09 22:05:52',30,'挑战：资源少，但是要同时支持美国、泰国、欧洲、新加坡系统建设
计划：
一、多国基建产品PIM+Listing：多主体&多销售&多币种下，清关、采购以及系统间流转自动化；运费管理、运输方式交期管理、PDP文件展示管理等能力升级
二、美国站：移动端App搭建资源筹备及路线规划：人力招募，技术调研，产品设计，UI设计等；
三、以泰国业务为起点搭建海外客户系统对接平台（B2B Connect Platform）1.0，目标冲刺530具备BYD泰国可对接；欧洲站需求分析和路线规划。',48,'',0,1,'yefen.chen','yefen.chen','yefen.chen','陈叶芬');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (191,'2025-05-09 22:05:52','2025-05-12 15:00:57',30,'-',69,'',1,1,'yefen.chen','yefen.chen','yefen.chen','陈叶芬');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (192,'2025-05-09 22:05:52','2025-05-12 15:01:00',30,'-',97,'',1,1,'yefen.chen','yefen.chen','yefen.chen','陈叶芬');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (193,'2025-05-09 22:05:52','2025-05-12 15:01:03',30,'-',138,'',1,1,'yefen.chen','yefen.chen','yefen.chen','陈叶芬');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (194,'2025-05-10 14:04:42','2025-05-10 14:04:42',31,'一、AI物料管家接口需求，排期要5月15日左右提供给到坤同：
1）用户导入商品清单后，AI需匹配出最适合的5种SKU，并根据排序条件从高到低排序
2）匹配物料的统计范围为匹配度最高的SKU的三级目录为边界，不属于此三级目录的SKU不纳入筛选范畴
二、EVM商城有一个订单打标和调仓逻辑调整需要BOSS系统支持，已经拉通，下周一做需求评审。
三、自由品牌替换agent匹配结果调优，坤同这边自主完成。
四、视觉智能柜立项、供应商选型。目前选了一家深圳做智能视觉柜的公司做了拜访，打算购买一台样机研究。
五、智能仓sass产品客户有做本地化需求，等客户报价。
六、海外有小仓库线索需求，目前无明确承接方。',49,'',0,1,'jianyong.zhou','jianyong.zhou','jianyong.zhou','周建勇');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (195,'2025-05-10 14:04:42','2025-05-12 15:00:45',31,'OKR明确，无大问题',70,'',1,1,'jianyong.zhou','jianyong.zhou','jianyong.zhou','周建勇');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (196,'2025-05-10 14:04:42','2025-05-12 15:00:48',31,'OKR明确，无大问题',98,'',1,1,'jianyong.zhou','jianyong.zhou','jianyong.zhou','周建勇');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (197,'2025-05-10 14:04:42','2025-05-12 15:00:51',31,'OKR明确，无大问题',140,'',1,1,'jianyong.zhou','jianyong.zhou','jianyong.zhou','周建勇');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (198,'2025-05-12 09:31:08','2025-05-12 09:31:08',27,'无',43,'',0,1,'jin.yang','jin.yang','jin.yang','杨锦');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (199,'2025-05-12 09:31:08','2025-05-12 09:31:08',27,'从安全技术、安全合规、安全管理方面，结合实际情况目前规划49项信息安全相关项目，本财年进度6%',67,'',0,1,'jin.yang','jin.yang','jin.yang','杨锦');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (200,'2025-05-12 09:31:08','2025-05-12 15:03:05',27,'已纳入日常跟踪，暂时不需要优化评估方案',93,'',1,1,'jin.yang','jin.yang','jin.yang','杨锦');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (201,'2025-05-12 09:31:08','2025-05-12 09:31:08',27,'暂无安全风险，需要持续进行风险评估',135,'',0,1,'jin.yang','jin.yang','jin.yang','杨锦');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (202,'2025-05-12 10:10:02','2025-05-12 10:10:02',4,'目前获取的数据，仅在商品百科（https://boss.zkh360.com/ptai-cube/）中供数据员日常查询使用。',9,'',0,1,'hongxia.si','hongxia.si','hongxia.si','司洪霞');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (203,'2025-05-12 10:10:02','2025-05-12 10:10:02',4,'1、缺少独立数据爬取资源，工程同学兼职，每周10个左右，数据爬取速度慢
2、利用AI进行数据结构化处理，品牌差异大，需要人机结合的处理流程，方案中
接下来两周重点：
1、快速上线数据结构化处理的线上产品方案，跑通验证',10,'',0,1,'hongxia.si','hongxia.si','hongxia.si','司洪霞');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (204,'2025-05-12 10:10:02','2025-05-12 10:10:02',4,'主要受不同品牌网站质量影响，会有质量差的网站爬取速度较慢，导致超时异常，目前都是本地机器人工监测，超时重试，效率会有一定影响。',72,'',0,1,'hongxia.si','hongxia.si','hongxia.si','司洪霞');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (205,'2025-05-12 10:10:02','2025-05-12 10:10:02',4,'主要卡点是前面提到问题挑战，需要主动探索突破，核心成员主动推进的紧迫感和积极性待提升',80,'',0,1,'hongxia.si','hongxia.si','hongxia.si','司洪霞');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (206,'2025-05-12 10:10:02','2025-05-12 15:02:23',4,'暂无',122,'',1,1,'hongxia.si','hongxia.si','hongxia.si','司洪霞');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (207,'2025-05-12 10:11:55','2025-05-12 10:11:55',25,'已完成数据主题划分，明确了 客户、商品、交易等主题。',39,'',0,1,'chenhao.mo','chenhao.mo','chenhao.mo','莫晨皓');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (208,'2025-05-12 10:11:55','2025-05-12 10:11:55',25,'目前数仓表有1.28万张，之前都是按需求建表，同一个场景可能存在多个表，并且字段统计逻辑不统一。接下来会按主题，统一梳理，调研各个数据生产团队，对表和字段进行整合。',40,'',0,1,'chenhao.mo','chenhao.mo','chenhao.mo','莫晨皓');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (209,'2025-05-12 10:11:55','2025-05-12 10:11:55',25,'目前存在数据标准化程度低的问题，会逐个主题进行梳理归类。',65,'',0,1,'chenhao.mo','chenhao.mo','chenhao.mo','莫晨皓');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (210,'2025-05-12 10:11:55','2025-05-12 10:11:55',25,'目前是数据中台统一牵头进行梳理，后续会邀请数据生产部门进行协作。暂时不需要外部方法论。',90,'',0,1,'chenhao.mo','chenhao.mo','chenhao.mo','莫晨皓');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (211,'2025-05-12 10:11:55','2025-05-12 10:11:55',25,'由数据中台部门统一牵头，多方配合。',136,'',0,1,'chenhao.mo','chenhao.mo','chenhao.mo','莫晨皓');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (212,'2025-05-12 10:12:20','2025-05-12 10:12:20',24,'客户内部政策或者约束，项目立项、走招标、确定中标单位整个过程较长，需要配合客户流程。
售前、高质量线索识别和转化数量不足。',37,'',0,1,'haiting.zhang','haiting.zhang','haiting.zhang','张海亭');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (213,'2025-05-12 10:12:20','2025-05-12 10:12:20',24,'销售培训课程制定和培训，提升销售售前能力和次数以及高质量线索识别和转化周期。
聚焦产品价值，推广典型客户案例交付后效果。',38,'',0,1,'haiting.zhang','haiting.zhang','haiting.zhang','张海亭');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (214,'2025-05-12 10:12:20','2025-05-12 10:12:20',24,'公司对典型客户案例制作推广的要求与客户接受度有挑战，计划推广渠道：震坤行官网、公众号、抖音、视频号
预计访问时间：看客户时间
采访安排：
1.客户方采访出镜人员：管理层领导1名，生产线员工1~2名
2.预计拍摄时长：4-5小时总共，人员部分约1小时
3.采访/拍摄地点：企业内部办公室/会议室、生产线边、企业外部环境（企业大楼、企业logo等）
目前只能按照客户可以接受的方式推进，线上渠道宣传和签约仪式，采访暂不接受。',62,'',0,1,'haiting.zhang','haiting.zhang','haiting.zhang','张海亭');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (215,'2025-05-12 10:12:20','2025-05-12 10:12:20',24,'从规模化维度看，经信委，国资委等对AI应用有强烈诉求；从商业化维度看，中咨数据（有数据，看重震坤行数据治理和客户解决方案），慧穗云（场景横向扩展）',92,'',0,1,'haiting.zhang','haiting.zhang','haiting.zhang','张海亭');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (216,'2025-05-12 10:16:43','2025-05-12 10:16:43',6,'售前：潜在客户覆盖、需求满足度分析
售中：商品数据质量、价格竞争力、交期竞争力评估
售后：退货、工单、商品质量问题评估',12,'',0,1,'hongxia.si','hongxia.si','hongxia.si','司洪霞');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (217,'2025-05-12 10:16:43','2025-05-12 10:16:43',6,'个人防护完成2版数据迭代，输出建议，待业务验证反馈
电气输出数据，待业务反馈',13,'',0,1,'hongxia.si','hongxia.si','hongxia.si','司洪霞');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (218,'2025-05-12 10:16:43','2025-05-12 10:16:43',6,'暂无',14,'',0,1,'hongxia.si','hongxia.si','hongxia.si','司洪霞');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (219,'2025-05-12 10:16:43','2025-05-12 15:00:07',6,'暂无',74,'',1,1,'hongxia.si','hongxia.si','hongxia.si','司洪霞');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (220,'2025-05-12 10:16:43','2025-05-12 15:00:11',6,'暂无',81,'',1,1,'hongxia.si','hongxia.si','hongxia.si','司洪霞');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (221,'2025-05-12 10:16:43','2025-05-12 15:00:16',6,'暂无',104,'',1,1,'hongxia.si','hongxia.si','hongxia.si','司洪霞');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (222,'2025-05-12 10:16:43','2025-05-12 10:16:43',6,'目前主要卡点在业务团队配合度，需要自上而下支持',124,'',0,1,'hongxia.si','hongxia.si','hongxia.si','司洪霞');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (223,'2025-05-12 10:19:39','2025-05-12 10:19:39',23,'从各渠道的转化漏斗看，增长主要还是百度广告和视频号达人广告的流量扶持。',36,'',0,1,'haiting.zhang','haiting.zhang','haiting.zhang','张海亭');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (224,'2025-05-12 10:19:39','2025-05-12 10:19:39',23,'线上渠道的注册转化率当前差异还是比较大，主要原因还是线上渠道陆续在铺，小红书和抖音刚开始推广，持续关注几个渠道的表现。线下渠道调研后，5.21号完善功能，提升产品体验后继续关注。',63,'',0,1,'haiting.zhang','haiting.zhang','haiting.zhang','张海亭');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (225,'2025-05-12 10:19:39','2025-05-12 10:19:39',23,'典型客户案例推广的各个渠道已经闭环，可以从神策看到转化数据。',91,'',0,1,'haiting.zhang','haiting.zhang','haiting.zhang','张海亭');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (226,'2025-05-12 10:19:39','2025-05-12 10:19:39',23,'跟进月度KPI，评估典型客户案例的贡献。',132,'',0,1,'haiting.zhang','haiting.zhang','haiting.zhang','张海亭');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (227,'2025-05-12 11:21:25','2025-05-12 11:21:25',26,'1、前后端研发及测试团队 AI 开发者工具企业版开通使用率达 100%；
2、提升代码 AI 占比的培训材料已完成，计划下周分享。',41,'',0,1,'yuqing.li','yuqing.li','yuqing.li','李玉清');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (228,'2025-05-12 11:21:25','2025-05-12 11:21:25',26,'编码智能体技术 + 标杆团队打造实施计划推进',42,'',0,1,'yuqing.li','yuqing.li','yuqing.li','李玉清');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (229,'2025-05-12 11:21:25','2025-05-12 14:59:58',26,'无',64,'',1,1,'yuqing.li','yuqing.li','yuqing.li','李玉清');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (230,'2025-05-12 11:21:25','2025-05-12 14:59:55',26,'无',95,'',1,1,'yuqing.li','yuqing.li','yuqing.li','李玉清');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (231,'2025-05-12 11:21:25','2025-05-12 14:59:46',26,'无',133,'',1,1,'yuqing.li','yuqing.li','yuqing.li','李玉清');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (232,'2025-05-12 11:28:36','2025-05-12 11:28:36',16,'1.升级了提升搜索召回率和准确率的召回策略和向量化召回模型
2.CTR指标追踪拆解为搜索全链路的过程性指标提升追踪，针对每个环节的指标找出影响指标的关键算法模型和工程优化策略
',22,'',0,1,'xuekun.zheng','xuekun.zheng','xuekun.zheng','郑学坤');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (233,'2025-05-12 11:28:36','2025-05-12 11:28:36',16,'过去两周虽然升级了向量化召回，但是多路召回策略上的重排策略依然效果不好，无法针对多路召回做相关性排序，这影响了整体的排序效果，未来2周最关键的升级是能够针对多路混合召回进行相关性重排模型的升级上线',23,'',0,1,'xuekun.zheng','xuekun.zheng','xuekun.zheng','郑学坤');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (234,'2025-05-12 11:28:36','2025-05-12 11:28:36',16,'有
1. 向量嵌入模型+文本BM25模型会评测TOP200的召回命中率和MRR指标
2.相关性排序模型我们会评测TOP30的召回命中率和MRR指标
3.CTR模型会评测模型的AUC指标
',52,'',0,1,'xuekun.zheng','xuekun.zheng','xuekun.zheng','郑学坤');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (235,'2025-05-12 11:28:36','2025-05-12 11:28:36',16,'我们针对当前的搜索做了评测基准和badcase分析，制定了从query理解、召回命中率提升、CTR模型升级的优化策略',56,'',0,1,'xuekun.zheng','xuekun.zheng','xuekun.zheng','郑学坤');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (236,'2025-05-12 11:28:36','2025-05-12 14:59:26',16,'重复问题',73,'',1,1,'xuekun.zheng','xuekun.zheng','xuekun.zheng','郑学坤');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (237,'2025-05-12 11:28:36','2025-05-12 11:28:36',16,'我们会在线上进行分桶切流实验来验证模型的有效性',78,'',0,1,'xuekun.zheng','xuekun.zheng','xuekun.zheng','郑学坤');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (238,'2025-05-12 11:28:36','2025-05-12 14:59:33',16,'重复问题',100,'',1,1,'xuekun.zheng','xuekun.zheng','xuekun.zheng','郑学坤');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (239,'2025-05-12 11:28:36','2025-05-12 14:59:37',16,'重复问题',128,'',1,1,'xuekun.zheng','xuekun.zheng','xuekun.zheng','郑学坤');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (240,'2025-05-12 11:29:53','2025-05-12 11:29:53',4,'68个至82个，目前还是研发阶段',9,'',0,1,'chen.qian','chen.qian','chen.qian','钱陈');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (241,'2025-05-12 11:29:53','2025-05-12 11:29:53',4,' 数据获取后，产研侧还在把品牌数据结构化过程中，无法快速使用到数据治理及发品场景中，未来需要产研加快节奏探索出新技术方案；
人员资源投入问题，目前看品牌获取的节奏绩效目标还是很有挑战，不仅仅是数据获取的问题，而是整体应用链条打通；',10,'',0,1,'chen.qian','chen.qian','chen.qian','钱陈');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (242,'2025-05-12 11:29:53','2025-05-12 11:29:53',4,'反扒的问题会影响数据获取异常，只会影响某个品牌的采集效率',72,'',0,1,'chen.qian','chen.qian','chen.qian','钱陈');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (243,'2025-05-12 11:29:53','2025-05-12 11:29:53',4,'有，主要为资源问题，RPA账号资源及产研投入资源，这两个问题影响了品牌数据获取量及数据应用，数据的应用迟迟无任何进展，无法对整体项目进行评估',80,'',0,1,'chen.qian','chen.qian','chen.qian','钱陈');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (244,'2025-05-12 11:29:53','2025-05-12 11:29:53',4,'接口的限制主要为反扒问题，比如：西门子，会影响爬取效率，权限问题：如克鲁勃，需要登录才能查询到具体的商品详情数据，目前大量的品牌数据为SPU概念，同时相应的数据需要识别PDF或图片，对于数据应用影响很大，无法通过系统自动识别，影响整个项目体系；',122,'',0,1,'chen.qian','chen.qian','chen.qian','钱陈');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (245,'2025-05-12 11:33:31','2025-05-12 11:33:31',28,'1.完成了对dify源码最新版本的升级，并与安全中心完成了打通，ZKH用户可以通过域账号直接完成登录
2.大模型网关接入层完成了架构搭建
',44,'',0,1,'xuekun.zheng','xuekun.zheng','xuekun.zheng','郑学坤');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (246,'2025-05-12 11:33:31','2025-05-12 11:33:31',28,'1.大模型网关服务开发，将模型的管理和使用（模型申请、授权、接入点、APIKey、资源权限管理、Dify调用打通）上线，产研灰度试用
2.震坤行模型供应商插件开发 
3.DIfy版本升级和性能优化',45,'',0,1,'xuekun.zheng','xuekun.zheng','xuekun.zheng','郑学坤');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (247,'2025-05-12 11:33:31','2025-05-12 14:58:39',28,'暂无',66,'',1,1,'xuekun.zheng','xuekun.zheng','xuekun.zheng','郑学坤');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (248,'2025-05-12 11:33:31','2025-05-12 14:59:11',28,'暂无',94,'',1,1,'xuekun.zheng','xuekun.zheng','xuekun.zheng','郑学坤');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (249,'2025-05-12 11:33:31','2025-05-12 14:59:18',28,'暂无',139,'',1,1,'xuekun.zheng','xuekun.zheng','xuekun.zheng','郑学坤');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (250,'2025-05-12 11:37:52','2025-05-12 11:37:52',5,'过程进展：四级目录属性规则治理完成数1148个、四级目录SKU回归治理完成数965个
目标进展：官网商品池近一年有业务基础项达标率超90%四级目录数59个
暂无问题，接下来两周的计划完成130个四级目录属性规则治理、110个四级目录SKU回归治理、完成6W个SKU基础项治理',11,'',0,1,'chen.qian','chen.qian','chen.qian','钱陈');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (251,'2025-05-12 11:37:52','2025-05-12 15:01:58',5,'暂无',76,'',1,1,'chen.qian','chen.qian','chen.qian','钱陈');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (252,'2025-05-12 11:37:52','2025-05-12 11:37:52',5,'根据之前大盘业绩覆盖目录筛选出的3700+重点目录范围【张翠提供】
公司大盘销售额92.6亿【含停用草稿】
重点目录近一年有业务SKU数：260W，销售额71亿，占比公司大盘76.7%
重点目录近一年有业务上官网SKU数：231.9W，销售额58亿，占比公司大盘62.6%
重点目录官网商品池近一年有业务SKU数：207.5W，销售额44.5亿，占比公司大盘48%
24财年数据治理属性平均2000/人/周【含SKU目录回归】
23财年治理基础项【属性+图片，不含SKU目录回归】平均800/人/周
25财年：参与基础项治理【属性+图片+SKU目录回归】人数20人，按照800/人/周提效50%估算，',79,'',0,1,'chen.qian','chen.qian','chen.qian','钱陈');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (253,'2025-05-12 11:37:52','2025-05-12 15:02:06',5,'同上',120,'',1,1,'chen.qian','chen.qian','chen.qian','钱陈');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (254,'2025-05-12 11:44:55','2025-05-12 11:44:55',6,'商品力整体水平，包括潜力客户渗透率、需求覆盖度、商品数据质量、价格竞争力、交期竞争力、服务水平(包括退货率、工单率商品质量问题率);以及各维度下的问题明细和改善策略。',12,'',0,1,'chen.qian','chen.qian','chen.qian','钱陈');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (255,'2025-05-12 11:44:55','2025-05-12 11:44:55',6,'日前已完成个人防护产线商品力整体分析和待改善数据和建议输出。已经拉通产线严重策略情况，目前产线反馈不是很及时，计划问题升级，从公司层面协助推进，尚未开展第2条产线试点.',13,'',0,1,'chen.qian','chen.qian','chen.qian','钱陈');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (256,'2025-05-12 11:44:55','2025-05-12 11:44:55',6,'目前是线下Excel在推进，还未系统化',14,'',0,1,'chen.qian','chen.qian','chen.qian','钱陈');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (257,'2025-05-12 11:44:55','2025-05-12 11:44:55',6,'目前是线下Excel在推进，还未系统化',74,'',0,1,'chen.qian','chen.qian','chen.qian','钱陈');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (258,'2025-05-12 11:44:55','2025-05-12 11:44:55',6,'试点产线暂未反馈，无法评估',81,'',0,1,'chen.qian','chen.qian','chen.qian','钱陈');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (259,'2025-05-12 11:44:55','2025-05-12 11:44:55',6,'试点产线暂未反馈，无法评估',104,'',0,1,'chen.qian','chen.qian','chen.qian','钱陈');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (260,'2025-05-12 11:44:55','2025-05-12 11:44:55',6,'第1条试点产线，待其反馈。需拉通产线协作，目前无法设置完成计划。可以讨论下团队构成及分工，建议把产线、定价、交期等其他角色拉入。',124,'',0,1,'chen.qian','chen.qian','chen.qian','钱陈');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (261,'2025-05-12 13:32:08','2025-05-12 13:32:08',15,'【定品实验】
核心动作：短期解决方案是新增一路文本召回模型，离线实验表明top30召回命中率提升7%。
电气类目Agent定品一致率：4月整体69.65%，5月（上半月）72.85%；泵管阀类目Agent定品一致率：4月整体37.25%，5月（上半月）36.04%。
【定价实验】
核心动作：4月25日上线 办公、电气、泵管阀、照明 四个类目接入定价大脑。
观测指标：30天成交率：3月 15.8%，4月 15.3%（未到期）。',20,'',0,1,'changliang.xie','changliang.xie','changliang.xie','谢常亮');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (262,'2025-05-12 13:32:08','2025-05-12 13:32:08',15,'【挑战一】
Agent定品一致率的主要瓶颈在于召回命中环节，召回相关性低。
下两周计划：为智能体提供统一的召回服务，并且top30结果利用相关性做过滤，提高top30召回命中率。
【挑战二】
老版询报价不断有紧急需求，需投入资源相应需求，影响新版询报价系统的资源投入。如公司级PD目标降本替换的策略执行，在询报价系系统各种落地“贸易品牌”打标以及SKU替换推荐。
下两周计划：新版当前进度整体完成15%；预计两周后销售端工作台完成90%，整体开发进度40%左右。',21,'',0,1,'changliang.xie','changliang.xie','changliang.xie','谢常亮');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (263,'2025-05-12 13:32:08','2025-05-12 14:58:17',15,'/',58,'',1,1,'changliang.xie','changliang.xie','changliang.xie','谢常亮');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (264,'2025-05-12 13:32:08','2025-05-12 14:58:22',15,'/',86,'',1,1,'changliang.xie','changliang.xie','changliang.xie','谢常亮');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (265,'2025-05-12 13:32:08','2025-05-12 15:01:43',15,'/',121,'',1,1,'changliang.xie','changliang.xie','changliang.xie','谢常亮');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (266,'2025-05-12 13:57:18','2025-05-12 13:57:18',7,'SEO：
1、4月SEO新动销金额：731万（加上比亚迪1171万，和3月持平）（9月目标累计6000万，需要尽快达到月1000万）
2、本周新增2家SEO客户，累计110家（9月目标：130家）
3、搜索TOP20占比33%，较上月下降6个点（9月目标：45%）；原因：航天科技取消SEO，TOP20占比从52%降至12%。

AI推品：MTD动销54.03万。',15,'',0,1,'rui2.li','rui2.li','rui2.li','李瑞');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (267,'2025-05-12 13:57:18','2025-05-12 14:56:19',7,'-',16,'',1,1,'rui2.li','rui2.li','rui2.li','李瑞');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (268,'2025-05-12 13:57:18','2025-05-12 13:57:18',7,'SEO现有模型准确率较低，仍有客诉风险。新6.0模型准确率待验证提升。',18,'',0,1,'rui2.li','rui2.li','rui2.li','李瑞');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (269,'2025-05-12 13:57:18','2025-05-12 13:57:18',7,'Agent运营自搭建部分在正常规划中。
Agent与系统衔接问题，需额外申请产研资源支持。',51,'',0,1,'rui2.li','rui2.li','rui2.li','李瑞');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (270,'2025-05-12 13:57:18','2025-05-12 15:01:31',7,'-',82,'',1,1,'rui2.li','rui2.li','rui2.li','李瑞');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (271,'2025-05-12 13:57:18','2025-05-12 13:57:18',7,'运营自己产能问题还未正式启动。',125,'',0,1,'rui2.li','rui2.li','rui2.li','李瑞');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (272,'2025-05-12 13:59:43','2025-05-12 13:59:43',17,'过去两周，AI搜索经历开发阶段/测试阶段/灰度上线，已经完成在官网的上线。
能力提升部分：
① 支持与传统搜索框进行同步联动：左侧仍由传统搜索展示结果，右侧聊天框则进行精确定品；
② 在用户搜索过程中，增加需求澄清的步骤，利用单独的需求澄清agent，一步步引导用户补充完善产品参数和品牌；
③ 支持用户询问具体商品的产品参数。',24,'',0,1,'changliang.xie','changliang.xie','changliang.xie','谢常亮');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (273,'2025-05-12 13:59:43','2025-05-12 13:59:43',17,'定品准确性有待进一步提升；大模型回复速度需要优化，导致左侧搜索比右侧快很多；大模型对于图片的识别速度偏慢。
后两周工作：提升定品agent能力，评测定品准确性，优化前端交互，上线图片搜品功能。',25,'',0,1,'changliang.xie','changliang.xie','changliang.xie','谢常亮');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (274,'2025-05-12 13:59:43','2025-05-12 14:55:58',17,'/',54,'',1,1,'changliang.xie','changliang.xie','changliang.xie','谢常亮');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (275,'2025-05-12 13:59:43','2025-05-12 14:56:05',17,'/',83,'',1,1,'changliang.xie','changliang.xie','changliang.xie','谢常亮');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (276,'2025-05-12 13:59:43','2025-05-12 14:55:54',17,'/',126,'',1,1,'changliang.xie','changliang.xie','changliang.xie','谢常亮');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (277,'2025-05-12 14:18:04','2025-05-12 14:18:04',19,'官网DAU：4月14日-4月25日 9994，4月28日-5月09日 9684。受五一节影响有所下降。
最近2周主要投入在AI行家助手上官网上，还没有对客开放，DAU驱动因素体现不明显。',28,'',0,1,'changliang.xie','changliang.xie','changliang.xie','谢常亮');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (278,'2025-05-12 14:18:04','2025-05-12 14:18:04',19,'主要问题与挑战：
1）AI行家助手定品服务暂时不健全，导致AI搜索定品不准。
2）官网现有前端架构，对于AI行家助手兼容性较差。
未来两周计划：AI行家助手上官网二期，主要攻克页面兼容以及定品问题。',29,'',0,1,'changliang.xie','changliang.xie','changliang.xie','谢常亮');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (279,'2025-05-12 14:18:04','2025-05-12 14:58:52',19,'/',59,'',1,1,'changliang.xie','changliang.xie','changliang.xie','谢常亮');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (280,'2025-05-12 14:18:04','2025-05-12 14:58:58',19,'/',84,'',1,1,'changliang.xie','changliang.xie','changliang.xie','谢常亮');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (281,'2025-05-12 14:18:04','2025-05-12 14:59:02',19,'/',127,'',1,1,'changliang.xie','changliang.xie','changliang.xie','谢常亮');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (282,'2025-05-12 14:21:11','2025-05-12 14:21:11',7,'1、推品：新增客户清单52单/85.9w行（累计808.7w），新增替换关系数据129行（累计239225）
2、SEO：闻道同义词17478660/高质量1167056 （同步完成）。新增客户商城监测数据299081行(累计5967120行)',15,'',0,1,'hongxia.si','hongxia.si','hongxia.si','司洪霞');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (283,'2025-05-12 14:21:11','2025-05-12 14:21:11',7,'1、推品：运算次数：常采清单计算85.9万次、输出策略次数（累计93个推品任务）,被采纳次数（推品21个客户/新推品17.8w sku）
2、SEO：运算次数0行（累计10415892行），输出策略次数0个任务/0行（累计3565个任务/10415892行），被采纳次数0个任务/0行（累计3565个任务/10415892行），新增优质商品标题0个（累计10157602个 )
说明：SEO近两周仅做已优化标题问题调不做SEO优化大批量更新，计划待SKU维度新策上线后统一大批量实施',16,'',0,1,'hongxia.si','hongxia.si','hongxia.si','司洪霞');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (284,'2025-05-12 14:21:11','2025-05-12 14:21:11',7,'1、推品：推品策略和推品引擎服务升级优化推进中，产品经理精力问题进展略缓
2、SEO：SKU标题优化策略评测结果不达预期，提示词继续优化中，评测耗时（已协调资源支持）
接下来两周
1、推品：引擎服务升级技术方案确定，推品策略监测数据完善
2、SEO：sku拓展词策略调优评测，sku拓展词+类目词混合应用产品方案确定',18,'',0,1,'hongxia.si','hongxia.si','hongxia.si','司洪霞');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (285,'2025-05-12 14:21:11','2025-05-12 14:21:11',7,'1、目前推品到客户商城是否上架/是否展示，缺乏有效的客户状态对接和回传，需要人工参与确认
2、推品大脑销售导出数据，线下确认后推品上架数据，数据效果追踪计算数据量大-技术评估中',51,'',0,1,'hongxia.si','hongxia.si','hongxia.si','司洪霞');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (286,'2025-05-12 14:21:11','2025-05-12 14:21:11',7,'目前系统基建和链路还不够通畅，人工参与/异常多，影响客户覆盖速度和自动化率，需要升级基础能力：
1、推品上架-客制化字段（类目/品牌/规格映射/比价/竞品链接等）自动补全链路升级
2、SEO全链路标题一致性问题：SEO客户商城展示标题vs客户商品标题vszkh订单开票一致性问题会导致部分客户无法覆盖（如比亚迪），需要商品池信息管理+ESP+订单系统升级',82,'',0,1,'hongxia.si','hongxia.si','hongxia.si','司洪霞');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (287,'2025-05-12 14:21:11','2025-05-12 14:21:11',7,'客制化差异大，之后后续配合运营团队，by客户做整体的评估分析',125,'',0,1,'hongxia.si','hongxia.si','hongxia.si','司洪霞');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (288,'2025-05-12 14:23:22','2025-05-12 14:23:22',20,'1. AI销售助手APP周UV 23，PV 258；7日留存38.98%，区域销售渗透率3.46%（基数655）。
2. esp下单抬头合规有效性占比99.7%，预计下周全量切换，达到100%。',30,'',0,1,'changliang.xie','changliang.xie','changliang.xie','谢常亮');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (289,'2025-05-12 14:23:22','2025-05-12 14:23:22',20,'●问题和挑战
1、APP线上体验存在部分细节问题，已协调测试介入，530推进问题全部解决，保障整体用户体验
2、客服主数据字段增加投入前端资源较多，运营客户服务建议相关需求排期可能会滞后
3、销售区域化需求业务要求时间较为紧急，已拉通各业务和系统相关方初步确定6月份后进入开发
下两周计划：
1. 新增联系人支持手动调整，自动推送版本更新，查询客户合作情况
2. APP集成消息中心，增加客户服务建议场景
3. 客服客户在线交接开发测试完成
4. 客户主数据字段增加 DTC 项目部分',31,'',0,1,'changliang.xie','changliang.xie','changliang.xie','谢常亮');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (290,'2025-05-12 14:23:22','2025-05-12 14:55:14',20,'/',55,'',1,1,'changliang.xie','changliang.xie','changliang.xie','谢常亮');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (291,'2025-05-12 14:23:22','2025-05-12 14:55:22',20,'/',85,'',1,1,'changliang.xie','changliang.xie','changliang.xie','谢常亮');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (292,'2025-05-12 14:23:22','2025-05-12 14:55:29',20,'/',129,'',1,1,'changliang.xie','changliang.xie','changliang.xie','谢常亮');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (293,'2025-05-12 14:27:16','2025-05-12 14:34:58',20,'-',30,'',1,1,'changliang.xie','changliang.xie','changliang.xie','谢常亮');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (298,'2025-05-12 14:50:19','2025-05-12 14:50:19',8,'定价大脑接入询报价，4.25~5.7调用定价大脑次数 4300次，效果数据待持续追踪
定价大脑-会员价覆盖sku：533.5万/66.7%',17,'',0,1,'hongxia.si','hongxia.si','hongxia.si','司洪霞');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (299,'2025-05-12 14:50:19','2025-05-12 14:50:19',8,'价格相关系统评估梳理，发现价格入口端多，缺乏统一价格监控管理
https://wiki.zkh360.com/confluence/pages/viewpage.action?pageId=348553376
接下来重点：
1、面价/会员价，全量新建/调价入口监控，入口统一可行性评估
2、客户协议价，落客户商品池存储管理产品方案评估',19,'',0,1,'hongxia.si','hongxia.si','hongxia.si','司洪霞');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (300,'2025-05-12 14:50:19','2025-05-12 14:50:19',8,'会员价统一策略覆盖：第一批产线都已覆盖：办公、电气低压、泵管阀、搬运、照明；在进行中的产线有：化学试剂、实验室仪器耗材、个人防护，进度正常
客制化定价模型：模型数据指标梳理，待BI准备数据，投入度不足，进度慢',71,'',0,1,'hongxia.si','hongxia.si','hongxia.si','司洪霞');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (301,'2025-05-12 14:50:19','2025-05-12 14:50:19',8,'会员价统一管理，目前产线覆盖推进进度正常
',77,'',0,1,'hongxia.si','hongxia.si','hongxia.si','司洪霞');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (302,'2025-05-12 14:50:19','2025-05-12 14:50:19',8,'问题：数据监控分析，以后续模型如何使用进度相对滞后，需要加大投入',123,'',0,1,'hongxia.si','hongxia.si','hongxia.si','司洪霞');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (303,'2025-05-12 15:21:22','2025-05-12 15:21:22',21,'用户数73，对话数374，场景数12个',32,'',0,1,'hongxia.si','hongxia.si','hongxia.si','司洪霞');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (304,'2025-05-12 15:21:22','2025-05-12 15:21:22',21,'开发工程师与业务直接对接进行新场景agent开发和调试为主，缺乏整体项目规划和运营。接下来，产品经理加入以项目制方式整体推进：
1、确定整体项目计划，场景优先级和排期
2、搭建项目指标和监测看版
3、确定已上线agent的运营推广计划',33,'',0,1,'hongxia.si','hongxia.si','hongxia.si','司洪霞');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (305,'2025-05-12 15:21:22','2025-05-12 15:21:22',21,'主要阻力：业务配合上没阻力，主要还是场景选择和投产比的问题
1、场景过于复杂：业务测提报的很多场景相对复杂，不是简单的Agent可实现的，需要依赖于业务系统系统基建完善；如定制品场景
2、场景过于狭窄：仅适用于少量内部用的低频场景，投入产出比低，如供应商审核
需要做好场景筛选，找到基建相对成熟，且业务场景高频、用户量大的高优场景',60,'',0,1,'hongxia.si','hongxia.si','hongxia.si','司洪霞');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (306,'2025-05-12 15:21:22','2025-05-12 15:21:22',21,'1、Agent困难度还是跟业务场景的复杂度和基建完善度相关，如定制品场景，需要从定制品的商品管理/规范定义，定制品展示/组合下单，定制品供应商协同传递等全链路熟路业务流程，完善系统基建，Agent只能解决入口测服务效率，无法加速系统基建和业务规则完善进度。
',89,'',0,1,'hongxia.si','hongxia.si','hongxia.si','司洪霞');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (307,'2025-05-12 15:21:22','2025-05-12 15:21:22',21,'目前渗透率还非常低，已上线agent缺乏运营推广，问答响应准确率也缺乏系统评测，badcase日常反馈少',131,'',0,1,'hongxia.si','hongxia.si','hongxia.si','司洪霞');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (308,'2025-05-12 15:51:55','2025-05-12 15:51:55',15,'电气定品采纳率=70%，泵管阀定品采纳率=40%，暂未达到理想预期',20,'',0,1,'hongxia.si','hongxia.si','hongxia.si','司洪霞');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (309,'2025-05-12 15:51:55','2025-05-12 15:51:55',15,'主要是召回模型召回不准+定品大模型自身幻觉等不稳定的问题导致定品采纳率不高，且目前解决方案更偏技术侧探索，因为优化进度及效果目标不够清晰。
接下来工作：
目前已经拆解了不同badcase类型，定位原因，推进技术优化，持续跟进优化结果
详情：
https://fyfjmeylai.feishu.cn/docx/LKj4dPaRtoLZhKxHRdzcVEFwnzf',21,'',0,1,'hongxia.si','hongxia.si','hongxia.si','司洪霞');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (310,'2025-05-12 15:51:55','2025-05-12 15:51:55',15,'此项目由运营同步',58,'',0,1,'hongxia.si','hongxia.si','hongxia.si','司洪霞');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (311,'2025-05-12 15:51:55','2025-05-12 15:51:55',15,'目前聚焦在电气、泵管阀两个产线的badcase分析优化上，暂未扩大评测优化范围',86,'',0,1,'hongxia.si','hongxia.si','hongxia.si','司洪霞');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (312,'2025-05-12 15:51:55','2025-05-12 15:51:55',15,'电气badcase主要分类：95%+集中在召回侧对需求理解和质量问题，包括：90%召回商品相关性低且召回不全（需求理解不完整），3.8%零召回（需求修改需求/类目预测错误等），1.4%需求关键参数缺失或需求有误

泵管阀的badcase问题分布比较多远，其中：
25%为召回问题
52.5%为定品问题，包括：15%大模型幻觉，15%需求入参不完整，22.5%有sku但定品结果不完整；
15%为推优问题

badcase分析详情：https://doc.weixin.qq.com/doc/w3_AasA_gacAKYCNjMmYEXCyQCO8Dw5d?scode=AAcAtAcPAAkybm7
',121,'',0,1,'hongxia.si','hongxia.si','hongxia.si','司洪霞');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (313,'2025-05-12 17:39:09','2025-05-12 17:39:09',16,'向量1.5版本模型上线，针对部分非精准匹配的带品名词的搜索有一定正向效果。模型待持续调优，基于query词性和召回技术方案的调优路径可行。但算法进一步强化关键词理解仍有较大空间。',22,'',0,1,'rui2.li','rui2.li','rui2.li','李瑞');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (314,'2025-05-12 17:39:09','2025-05-12 17:39:09',16,'难点：系统模型升级后对整体CTR提升效果不够明显，需进一步调优。
运营工作计划：推动产品升级筛选项交互，降低泛词占比。推动搜索3N营销位逻辑升级。',23,'',0,1,'rui2.li','rui2.li','rui2.li','李瑞');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (315,'2025-05-12 17:39:09','2025-05-12 17:41:24',16,'-',52,'',1,1,'rui2.li','rui2.li','rui2.li','李瑞');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (316,'2025-05-12 17:39:09','2025-05-12 17:41:26',16,'-',56,'',1,1,'rui2.li','rui2.li','rui2.li','李瑞');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (317,'2025-05-12 17:39:09','2025-05-12 17:41:28',16,'-',73,'',1,1,'rui2.li','rui2.li','rui2.li','李瑞');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (318,'2025-05-12 17:39:09','2025-05-12 17:41:30',16,'-',78,'',1,1,'rui2.li','rui2.li','rui2.li','李瑞');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (319,'2025-05-12 17:39:09','2025-05-12 17:41:32',16,'-',100,'',1,1,'rui2.li','rui2.li','rui2.li','李瑞');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (320,'2025-05-12 17:39:09','2025-05-12 17:41:35',16,'-',128,'',1,1,'rui2.li','rui2.li','rui2.li','李瑞');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (321,'2025-05-12 17:39:12','2025-05-12 17:39:12',18,'○商城GMV 9556万，YOY +1.5%（去年同期 9414万），其中主要来自CD类客户增长，但A类客户采购大幅下降（-864万），主要为东方电气、普洛斯的下单渠道转移到了对接渠道。
CD类客户增长上，中小客规模化运营对CD非集团客户有所驱动。
',26,'',0,1,'rui2.li','rui2.li','rui2.li','李瑞');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (322,'2025-05-12 17:39:12','2025-05-12 17:41:20',18,'-',27,'',1,1,'rui2.li','rui2.li','rui2.li','李瑞');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (323,'2025-05-12 17:39:12','2025-05-12 17:41:05',18,'-',53,'',1,1,'rui2.li','rui2.li','rui2.li','李瑞');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (324,'2025-05-12 17:39:12','2025-05-12 17:39:12',18,'围绕PACE调整商品竞争力',57,'',0,1,'rui2.li','rui2.li','rui2.li','李瑞');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (325,'2025-05-12 17:39:12','2025-05-12 17:41:08',18,'-',75,'',1,1,'rui2.li','rui2.li','rui2.li','李瑞');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (326,'2025-05-12 17:39:12','2025-05-12 17:41:10',18,'-',87,'',1,1,'rui2.li','rui2.li','rui2.li','李瑞');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (327,'2025-05-12 17:39:12','2025-05-12 17:41:12',18,'-',99,'',1,1,'rui2.li','rui2.li','rui2.li','李瑞');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (328,'2025-05-12 17:39:12','2025-05-12 17:41:14',18,'-',130,'',1,1,'rui2.li','rui2.li','rui2.li','李瑞');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (329,'2025-05-12 17:41:50','2025-05-12 17:41:50',22,'新进客户群数：605；累计进群数：16954
新进供应商群数：88；累计进群数：3329

自动回答问题占比：统计中，待补充',34,'',0,1,'changliang.xie','changliang.xie','changliang.xie','谢常亮');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (330,'2025-05-12 17:41:50','2025-05-12 17:41:50',22,'问题和挑战：提升需求澄清的准确性；RPA稳定性有一定波动
工作计划：
① 继续优化ai搜索的能力和交互，开放客户进入灰度测试；
② 完善知识库产品方案，思考知识库的核心价值/高频应用场景；
③ 完成业务需求-sku价格变动推送',35,'',0,1,'changliang.xie','changliang.xie','changliang.xie','谢常亮');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (331,'2025-05-12 17:41:50','2025-05-12 17:42:32',22,'/',61,'',1,1,'changliang.xie','changliang.xie','changliang.xie','谢常亮');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (332,'2025-05-12 17:41:50','2025-05-12 17:42:34',22,'/',88,'',1,1,'changliang.xie','changliang.xie','changliang.xie','谢常亮');
INSERT INTO `fe_mis_objective_answer` (`id`,`gmt_create`,`gmt_modified`,`objective_id`,`content`,`question_id`,`files`,`is_deleted`,`task_id`,`creator`,`updater`,`username`,`nickname`) VALUES (333,'2025-05-12 17:41:50','2025-05-12 17:42:36',22,'/',134,'',1,1,'changliang.xie','changliang.xie','changliang.xie','谢常亮');