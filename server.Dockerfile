FROM newhub.zkh360.com/fe/golang:1.24.3-alpine3.21 as base

FROM base AS builder
WORKDIR /app
COPY . .
ENV GO111MODULE=on \
    GOPROXY=https://goproxy.cn,direct \
    GIN_MODE=release
RUN cd /app/apps/server && go build -o /app/mis

FROM base AS runner
WORKDIR /app
COPY --from=builder /app/mis /app/mis
ARG ENV
ENV ENV=${ENV}
ENV PORT=3000

ARG DATABASE_URL
ENV DATABASE_URL=${DATABASE_URL}

ENV GIN_MODE=release

CMD ["/app/mis"]
