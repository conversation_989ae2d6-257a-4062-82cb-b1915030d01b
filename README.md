# RUN
需要安装[air](https://github.com/air-verse/air)
同时需要在PATH中添加go bin目录，在你的 shell 配置文件（如 ~/.zshrc 或 ~/.bashrc ）中添加：
```shell
export PATH=$PATH:$(go env GOPATH)/bin
```

# TO-DO
## 2025-05-19
- [x] 去除db中的job字段
- [x] 支持eslint
- [ ] 去除冗余api及相关代码
- [x] fix eslint error
- [x] 测试
- [x] 功能扩展（支持ai结果的重新生成，及问题删除）
- [x] 功能扩展（所有ai的生成提前，减少用户等待）
- [x] 支持图片上传（最多三张，不用ai处理）
- [x] 把第一期数据迁移过来
- [x] 发布release环境
- [x] 时间维度的OKR
