FROM newhub.zkh360.com/fe/nodejs:22.14.0-alpine3.21-pnpm10.6.2 as builder
WORKDIR /app
COPY .npmrc package.json pnpm-lock.yaml pnpm-workspace.yaml ./
RUN pnpm config set registry "https://registry.npmmirror.com/"
RUN pnpm install
COPY . ./
ARG PKG
ENV PKG=${PKG}
RUN pnpm --filter=* install
RUN pnpm --filter=$PKG run build

FROM newhub.zkh360.com/fe/ossutil-boss:1.7.19 AS uploader
WORKDIR /usr/local/bin
ARG PKG
ENV PKG=${PKG}
COPY --from=builder /app ./app
RUN ./ossutil cp -r app/apps/$PKG/dist/ oss://zkh360-boss/assets/mis/okr-$PKG -r -u --disable-ignore-error -e oss-cn-beijing.aliyuncs.com

FROM newhub.zkh360.com/fe/nginx:1.21.6-alpine
ARG ENV
ENV ENV=${ENV}
ARG PKG
ENV PKG=${PKG}
COPY --from=uploader /usr/local/bin/app/apps/$PKG/dist /opt/web
COPY --from=builder /app/robots.txt /opt/web/
COPY --from=uploader /usr/local/bin/app/web.$PKG.conf /etc/nginx/conf.d/
